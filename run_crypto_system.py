#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف التشغيل الرئيسي للنظام المتكامل للعملات المشفرة
النسخة النهائية - نموذج متقدم + تليجرام + تتبع الأداء
"""

import os
import sys
import time
import schedule
import logging
import subprocess
from datetime import datetime
from integrated_crypto_system import IntegratedCryptoSystem

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crypto_system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class CryptoSystemRunner:
    """مشغل النظام المتكامل"""
    
    def __init__(self):
        self.system = IntegratedCryptoSystem()
        self.running = False
        
    def run_single_analysis(self):
        """تشغيل تحليل واحد"""
        try:
            logger.info("🚀 بدء تحليل جديد...")
            
            start_time = time.time()
            results, stats = self.system.run_full_analysis()
            end_time = time.time()
            
            logger.info(f"✅ تم الانتهاء من التحليل في {end_time - start_time:.1f} ثانية")
            logger.info(f"📊 توصيات جديدة: {len(results)}")
            logger.info(f"📈 معدل النجاح: {stats['success_rate']:.1%}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل: {e}")
            return False
    
    def run_continuous(self, interval_hours=4):
        """تشغيل مستمر كل فترة محددة"""
        logger.info(f"🔄 بدء التشغيل المستمر كل {interval_hours} ساعات")
        
        # تشغيل فوري
        self.run_single_analysis()
        
        # جدولة التشغيل
        schedule.every(interval_hours).hours.do(self.run_single_analysis)
        
        self.running = True
        
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
                
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
            self.running = False
    
    def stop(self):
        """إيقاف النظام"""
        self.running = False
        logger.info("⏹️ تم إيقاف النظام")

def print_banner():
    """عرض شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                   🚀 النظام المتكامل للعملات المشفرة                   ║
║                                                              ║
║  🧠 نموذج ذكي مدرب على 379 عملة و 115K+ عينة                      ║
║  📊 دقة 90% في التنبؤ بارتفاعات 8%+                              ║
║  📱 إرسال تلقائي للتوصيات عبر التليجرام                            ║
║  📈 تتبع وتقييم الأداء التلقائي                                   ║
║  ⚡ تحليل سريع ومتوازي لـ 30+ عملة                               ║
║                                                              ║
║  المطور: نظام الذكاء الاصطناعي المتقدم                            ║
║  الإصدار: النسخة النهائية 2025                                  ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """عرض القائمة الرئيسية"""
    menu = """
📋 خيارات التشغيل:

1️⃣  تشغيل تحليل واحد فوري (بيانات حية)
2️⃣  تشغيل مستمر كل 4 ساعات
3️⃣  تشغيل مستمر كل 6 ساعات
4️⃣  تشغيل مستمر كل 12 ساعة
5️⃣  🔄 مراقبة مستمرة ذكية (كل 5 دقائق)
6️⃣  🚀 نظام تلقائي متقدم (تحديث + تحليل)
7️⃣  🧠 النظام المستمر الذكي (كل ساعة + تحسين ذاتي)
8️⃣  عرض إحصائيات الأداء
9️⃣  🔧 إعداد التليجرام (أداة مساعدة)
🔟  تحديث إعدادات التليجرام
0️⃣  خروج

اختر رقم الخيار: """

    return input(menu).strip()

def show_performance_stats():
    """عرض إحصائيات الأداء"""
    try:
        system = IntegratedCryptoSystem()
        stats = system.load_performance_stats()
        
        print("\n" + "="*60)
        print("📊 إحصائيات أداء النظام")
        print("="*60)
        print(f"📈 إجمالي التوصيات: {stats['total_recommendations']}")
        print(f"✅ التوصيات الناجحة: {stats['successful_recommendations']}")
        print(f"📊 معدل النجاح: {stats['success_rate']:.1%}")
        print(f"⏰ آخر تحديث: {stats.get('last_updated', 'غير محدد')}")
        print("="*60)
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {e}")

def update_telegram_config():
    """تحديث إعدادات التليجرام"""
    print("\n🔧 تحديث إعدادات التليجرام")
    print("="*40)
    
    bot_token = input("أدخل Bot Token الجديد (اتركه فارغاً للاحتفاظ بالحالي): ").strip()
    chat_id = input("أدخل Chat ID الجديد (اتركه فارغاً للاحتفاظ بالحالي): ").strip()
    
    try:
        # قراءة ملف الإعدادات
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحديث التوكن إذا تم إدخاله
        if bot_token:
            content = content.replace(
                'TELEGRAM_CONFIG = {\n    "bot_token": "**********************************************",',
                f'TELEGRAM_CONFIG = {{\n    "bot_token": "{bot_token}",'
            )
        
        # تحديث معرف المجموعة إذا تم إدخاله
        if chat_id:
            content = content.replace(
                '"chat_id": "-1002208374853"',
                f'"chat_id": "{chat_id}"'
            )
        
        # حفظ الملف
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث إعدادات التليجرام بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإعدادات: {e}")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        'simple_crypto_model.h5',
        'simple_crypto_model_scaler.pkl',
        'config.py',
        'integrated_crypto_system.py'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ ملفات مفقودة: {missing_files}")
        print("تأكد من وجود جميع الملفات المطلوبة")
        return
    
    runner = CryptoSystemRunner()
    
    try:
        while True:
            choice = print_menu()
            
            if choice == '1':
                print("\n🚀 تشغيل تحليل فوري...")
                runner.run_single_analysis()
                
            elif choice == '2':
                print("\n🔄 تشغيل مستمر كل 4 ساعات...")
                runner.run_continuous(4)
                
            elif choice == '3':
                print("\n🔄 تشغيل مستمر كل 6 ساعات...")
                runner.run_continuous(6)
                
            elif choice == '4':
                print("\n🔄 تشغيل مستمر كل 12 ساعة...")
                runner.run_continuous(12)

            elif choice == '5':
                print("\n🔄 بدء المراقبة المستمرة الذكية...")
                print("⚠️ سيتم فتح نافذة جديدة للمراقبة")
                try:
                    subprocess.Popen([sys.executable, 'continuous_crypto_monitor.py'])
                    print("✅ تم بدء المراقبة المستمرة في نافذة منفصلة")
                except Exception as e:
                    print(f"❌ خطأ في بدء المراقبة: {e}")

            elif choice == '6':
                print("\n🚀 بدء النظام التلقائي المتقدم...")
                print("⚠️ سيتم فتح نافذة جديدة للنظام التلقائي")
                try:
                    subprocess.Popen([sys.executable, 'auto_update_system.py'])
                    print("✅ تم بدء النظام التلقائي في نافذة منفصلة")
                    print("📊 النظام سيحدث البيانات كل ساعة ويحلل كل 30 دقيقة")
                except Exception as e:
                    print(f"❌ خطأ في بدء النظام التلقائي: {e}")

            elif choice == '7':
                print("\n🧠 بدء النظام المستمر الذكي...")
                print("⚠️ سيتم فتح نافذة جديدة للنظام المستمر الذكي")
                try:
                    subprocess.Popen([sys.executable, 'continuous_smart_system.py'])
                    print("✅ تم بدء النظام المستمر الذكي في نافذة منفصلة")
                    print("🔍 النظام سيحلل كل ساعة ويقيم الأداء كل 30 دقيقة")
                    print("🧠 التحسين الذاتي مفعل بناءً على نتائج الأداء")
                except Exception as e:
                    print(f"❌ خطأ في بدء النظام المستمر الذكي: {e}")

            elif choice == '8':
                show_performance_stats()

            elif choice == '9':
                print("\n🔧 بدء أداة إعداد التليجرام...")
                try:
                    subprocess.run([sys.executable, 'telegram_setup_helper.py'])
                except Exception as e:
                    print(f"❌ خطأ في تشغيل أداة الإعداد: {e}")

            elif choice == '10':
                update_telegram_config()
                
            elif choice == '0':
                print("\n👋 شكراً لاستخدام النظام!")
                break
                
            else:
                print("\n❌ خيار غير صحيح، حاول مرة أخرى")
            
            if choice not in ['2', '3', '4', '5', '6', '7']:
                input("\nاضغط Enter للمتابعة...")
    
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام")
    
    finally:
        runner.stop()

if __name__ == "__main__":
    main()
