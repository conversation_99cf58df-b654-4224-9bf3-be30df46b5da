#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة مساعدة لإعداد التليجرام واكتشاف Chat ID
"""

import requests
import json

def get_bot_info(bot_token):
    """الحصول على معلومات البوت"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                bot_info = data['result']
                print("✅ البوت يعمل بشكل صحيح!")
                print(f"🤖 اسم البوت: {bot_info['first_name']}")
                print(f"📝 اسم المستخدم: @{bot_info['username']}")
                print(f"🆔 معرف البوت: {bot_info['id']}")
                return True
            else:
                print(f"❌ خطأ في البوت: {data['description']}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def get_chat_updates(bot_token):
    """الحصول على آخر الرسائل لاكتشاف Chat ID"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok'] and data['result']:
                print("\n📨 آخر الرسائل المستلمة:")
                print("="*50)
                
                chat_ids = set()
                
                for update in data['result'][-10:]:  # آخر 10 رسائل
                    if 'message' in update:
                        message = update['message']
                        chat = message['chat']
                        
                        chat_id = chat['id']
                        chat_type = chat['type']
                        chat_title = chat.get('title', chat.get('first_name', 'غير محدد'))
                        
                        print(f"💬 Chat ID: {chat_id}")
                        print(f"📝 النوع: {chat_type}")
                        print(f"🏷️ الاسم: {chat_title}")
                        print(f"📅 التاريخ: {message['date']}")
                        print("-" * 30)
                        
                        chat_ids.add(chat_id)
                
                if chat_ids:
                    print(f"\n🎯 Chat IDs المكتشفة: {list(chat_ids)}")
                    return list(chat_ids)
                else:
                    print("❌ لم يتم العثور على رسائل")
                    return []
            else:
                print("❌ لا توجد رسائل أو خطأ في البيانات")
                return []
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ خطأ في جلب الرسائل: {e}")
        return []

def test_send_message(bot_token, chat_id):
    """اختبار إرسال رسالة"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': '🧪 رسالة اختبار من نظام العملات المشفرة\n\n✅ إذا وصلتك هذه الرسالة، فإن الإعدادات صحيحة!',
            'parse_mode': 'HTML'
        }
        
        response = requests.post(url, data=data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                print("✅ تم إرسال رسالة الاختبار بنجاح!")
                return True
            else:
                print(f"❌ خطأ في إرسال الرسالة: {data['description']}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إرسال الرسالة: {e}")
        return False

def update_config_file(bot_token, chat_id):
    """تحديث ملف الإعدادات"""
    try:
        # قراءة ملف الإعدادات
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحديث التوكن
        content = content.replace(
            '"bot_token": "8057944321:AAGO95tbq43Y_aiTRwcUmOAqS-RNR4HSHY8"',
            f'"bot_token": "{bot_token}"'
        )
        
        # تحديث Chat ID
        content = content.replace(
            '"chat_id": "1234567890"',
            f'"chat_id": "{chat_id}"'
        )
        
        # حفظ الملف
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث ملف config.py بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الملف: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("🔧 أداة إعداد التليجرام للنظام المتكامل")
    print("="*50)
    
    # طلب Bot Token
    bot_token = input("\n🤖 أدخل Bot Token: ").strip()
    
    if not bot_token:
        print("❌ يجب إدخال Bot Token")
        return
    
    # اختبار البوت
    print("\n🔍 اختبار البوت...")
    if not get_bot_info(bot_token):
        print("❌ البوت لا يعمل، تحقق من التوكن")
        return
    
    # البحث عن Chat IDs
    print("\n🔍 البحث عن Chat IDs...")
    print("💡 تأكد من إرسال رسالة للبوت أو إضافته لمجموعة أولاً")
    
    chat_ids = get_chat_updates(bot_token)
    
    if not chat_ids:
        print("\n❌ لم يتم العثور على Chat IDs")
        print("💡 تأكد من:")
        print("   1. إرسال رسالة للبوت")
        print("   2. أو إضافة البوت لمجموعة وإرسال رسالة")
        return
    
    # اختيار Chat ID
    if len(chat_ids) == 1:
        selected_chat_id = chat_ids[0]
        print(f"\n🎯 تم اختيار Chat ID تلقائياً: {selected_chat_id}")
    else:
        print(f"\n🎯 تم العثور على {len(chat_ids)} Chat IDs:")
        for i, chat_id in enumerate(chat_ids, 1):
            print(f"   {i}. {chat_id}")
        
        try:
            choice = int(input("\nاختر رقم Chat ID: ")) - 1
            selected_chat_id = chat_ids[choice]
        except (ValueError, IndexError):
            print("❌ اختيار غير صحيح")
            return
    
    # اختبار الإرسال
    print(f"\n🧪 اختبار إرسال رسالة إلى {selected_chat_id}...")
    if not test_send_message(bot_token, selected_chat_id):
        print("❌ فشل اختبار الإرسال")
        return
    
    # تحديث ملف الإعدادات
    print("\n💾 تحديث ملف الإعدادات...")
    if update_config_file(bot_token, selected_chat_id):
        print("\n🎉 تم إعداد التليجرام بنجاح!")
        print("✅ يمكنك الآن تشغيل النظام")
    else:
        print("❌ فشل تحديث ملف الإعدادات")

if __name__ == "__main__":
    main()
