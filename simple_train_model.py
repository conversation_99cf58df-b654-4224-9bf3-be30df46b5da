#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام تدريب مبسط للنموذج - إطار زمني واحد (4h)
الهدف: تدريب سريع وفعال على البيانات المتاحة
"""

import os
import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import joblib
import warnings
warnings.filterwarnings('ignore')

# إعدادات التدريب
DATA_DIR = 'multi_timeframe_data/4h'
MODEL_SAVE_PATH = 'simple_crypto_model'
BATCH_SIZE = 64
EPOCHS = 50
SEQUENCE_LENGTH = 18

class SimpleCryptoTrainer:
    """مدرب مبسط للعملات المشفرة"""
    
    def __init__(self):
        self.scaler = RobustScaler()
        self.model = None
        
    def load_and_prepare_data(self):
        """تحميل وتحضير البيانات"""
        print("📊 تحميل البيانات من مجلد 4h...")
        
        if not os.path.exists(DATA_DIR):
            raise ValueError(f"مجلد البيانات غير موجود: {DATA_DIR}")
        
        csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]
        print(f"📁 وجد {len(csv_files)} ملف من إجمالي 379 عملة")
        
        all_sequences = []
        all_targets = []
        
        for i, filename in enumerate(csv_files):
            file_path = os.path.join(DATA_DIR, filename)
            
            try:
                df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                
                if len(df) < 100:
                    continue
                
                # اختيار الميزات الأساسية
                features = ['close', 'volume', 'rsi', 'macd', 'obv', 'cmf']
                available_features = [f for f in features if f in df.columns]
                
                if len(available_features) < 4:
                    continue
                
                # تنظيف البيانات
                df_clean = df[available_features].dropna()
                
                if len(df_clean) < SEQUENCE_LENGTH + 20:
                    continue
                
                # تطبيع البيانات
                scaled_data = self.scaler.fit_transform(df_clean)
                
                # إنشاء التسلسلات والأهداف
                sequences, targets = self.create_sequences_and_targets(df_clean, scaled_data)
                
                if len(sequences) > 0:
                    all_sequences.extend(sequences)
                    all_targets.extend(targets)
                
                if (i + 1) % 10 == 0:
                    print(f"📈 تم معالجة {i + 1}/{len(csv_files)} ملف...")
                    
            except Exception as e:
                print(f"❌ خطأ في {filename}: {e}")
                continue
        
        if len(all_sequences) == 0:
            raise ValueError("لا توجد بيانات صالحة للتدريب")
        
        X = np.array(all_sequences)
        y = np.array(all_targets)
        
        print(f"✅ تم تحضير {len(X)} عينة تدريبية")
        print(f"📊 شكل البيانات: {X.shape}")
        print(f"🎯 توزيع الأهداف: {np.mean(y):.1%} إيجابي")
        
        return X, y
    
    def create_sequences_and_targets(self, df_original, scaled_data):
        """إنشاء التسلسلات والأهداف"""
        sequences = []
        targets = []
        
        for i in range(SEQUENCE_LENGTH, len(scaled_data) - 12):
            # التسلسل
            sequence = scaled_data[i-SEQUENCE_LENGTH:i]
            sequences.append(sequence)
            
            # الهدف: هل سيرتفع السعر 8%+ في الـ 12 فترة القادمة؟
            current_price = df_original['close'].iloc[i]
            future_prices = df_original['close'].iloc[i+1:i+13]
            
            max_future_price = future_prices.max()
            price_change = (max_future_price - current_price) / current_price
            
            target = 1 if price_change >= 0.08 else 0  # 8% ارتفاع
            targets.append(target)
        
        return sequences, targets
    
    def build_model(self, input_shape):
        """بناء النموذج المبسط"""
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            Dropout(0.3),
            BatchNormalization(),
            
            LSTM(64, return_sequences=False),
            Dropout(0.3),
            BatchNormalization(),
            
            Dense(32, activation='relu'),
            Dropout(0.2),
            
            Dense(1, activation='sigmoid')
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        return model
    
    def train(self):
        """تدريب النموذج"""
        print("🧠 بدء التدريب المبسط...")
        
        # تحميل البيانات
        X, y = self.load_and_prepare_data()
        
        # تقسيم البيانات
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
        )
        
        print(f"📊 بيانات التدريب: {len(X_train)}")
        print(f"📊 بيانات التحقق: {len(X_val)}")
        print(f"📊 بيانات الاختبار: {len(X_test)}")
        
        # بناء النموذج
        self.model = self.build_model((X_train.shape[1], X_train.shape[2]))
        
        print("🏗️ بنية النموذج:")
        self.model.summary()
        
        # callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-6,
                verbose=1
            )
        ]
        
        # التدريب
        print("🚀 بدء التدريب...")
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            batch_size=BATCH_SIZE,
            epochs=EPOCHS,
            callbacks=callbacks,
            verbose=1
        )
        
        # التقييم
        print("📊 تقييم النموذج...")
        test_loss, test_acc, test_prec, test_rec = self.model.evaluate(X_test, y_test, verbose=0)
        
        print(f"📈 دقة الاختبار: {test_acc:.3f}")
        print(f"📈 دقة التنبؤ: {test_prec:.3f}")
        print(f"📈 الاستدعاء: {test_rec:.3f}")
        
        # تقرير مفصل
        y_pred = (self.model.predict(X_test, verbose=0) > 0.5).astype(int)
        print("\n📊 تقرير التصنيف:")
        print(classification_report(y_test, y_pred))
        
        # حفظ النموذج
        self.save_model()
        
        return history
    
    def save_model(self):
        """حفظ النموذج والمعايرات"""
        self.model.save(f'{MODEL_SAVE_PATH}.h5')
        joblib.dump(self.scaler, f'{MODEL_SAVE_PATH}_scaler.pkl')
        print(f"✅ تم حفظ النموذج: {MODEL_SAVE_PATH}")
    
    def load_model(self):
        """تحميل النموذج والمعايرات"""
        try:
            self.model = tf.keras.models.load_model(f'{MODEL_SAVE_PATH}.h5')
            self.scaler = joblib.load(f'{MODEL_SAVE_PATH}_scaler.pkl')
            print(f"✅ تم تحميل النموذج: {MODEL_SAVE_PATH}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج: {e}")
            return False
    
    def predict(self, data):
        """التنبؤ"""
        if self.model is None:
            raise ValueError("النموذج غير محمل")
        
        # تحضير البيانات
        features = ['close', 'volume', 'rsi', 'macd', 'obv', 'cmf']
        available_features = [f for f in features if f in data.columns]
        
        if len(available_features) < 4:
            return None
        
        df_clean = data[available_features].dropna()
        
        if len(df_clean) < SEQUENCE_LENGTH:
            return None
        
        # تطبيع البيانات
        scaled_data = self.scaler.transform(df_clean)
        
        # أخذ آخر تسلسل
        sequence = scaled_data[-SEQUENCE_LENGTH:].reshape(1, SEQUENCE_LENGTH, -1)
        
        # التنبؤ
        prediction = self.model.predict(sequence, verbose=0)[0][0]
        
        return {
            'probability': float(prediction),
            'signal': prediction > 0.5
        }

def main():
    """الوظيفة الرئيسية"""
    print("🧠 نظام التدريب المبسط للعملات المشفرة")
    print("🎯 الهدف: تدريب سريع على بيانات 4h")
    
    try:
        trainer = SimpleCryptoTrainer()
        history = trainer.train()
        
        print("\n✅ تم التدريب بنجاح!")
        print("🎯 النموذج جاهز للاستخدام")
        
    except Exception as e:
        print(f"❌ خطأ في التدريب: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
