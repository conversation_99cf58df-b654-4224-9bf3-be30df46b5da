{"rsi": {"total_signals": 18779, "successful_signals": 5280, "average_return": 0.01715670256766691, "success_probability": 0.28116513126364556, "sharpe_ratio": 0.4967920377442092, "weight": 0.04899561620265543}, "macd": {"total_signals": 183276, "successful_signals": 36753, "average_return": 0.013964792004310247, "success_probability": 0.20053362142342696, "sharpe_ratio": 0.35855942791747203, "weight": 0.028443537579105192}, "adx": {"total_signals": 184073, "successful_signals": 41013, "average_return": 0.015167168997819709, "success_probability": 0.22280834234244024, "sharpe_ratio": 0.38510712296231, "weight": 0.03432400167913459}, "obv": {"total_signals": 185251, "successful_signals": 37725, "average_return": 0.014209953111104649, "success_probability": 0.20364262541092895, "sharpe_ratio": 0.37296305988679734, "weight": 0.02939160303820536}, "cmf": {"total_signals": 148772, "successful_signals": 29764, "average_return": 0.01329201895898406, "success_probability": 0.20006452827144894, "sharpe_ratio": 0.350538552517648, "weight": 0.027009900704629938}, "rsi + macd": {"total_signals": 1217, "successful_signals": 295, "average_return": 0.014892420898494133, "success_probability": 0.24239934264585045, "sharpe_ratio": 0.4478051174917921, "weight": 0.03666559026154366}, "rsi + adx": {"total_signals": 15465, "successful_signals": 4498, "average_return": 0.01808443192090941, "success_probability": 0.2908503071451665, "sharpe_ratio": 0.5344436925666534, "weight": 0.05342399227079127}, "rsi + obv": {"total_signals": 4385, "successful_signals": 1349, "average_return": 0.02049747402904941, "success_probability": 0.30763968072976056, "sharpe_ratio": 0.6437199766699988, "weight": 0.0640478621329287}, "rsi + cmf": {"total_signals": 745, "successful_signals": 204, "average_return": 0.013809974267557288, "success_probability": 0.2738255033557047, "sharpe_ratio": 0.44681307469947407, "weight": 0.03840862014697219}, "macd + adx": {"total_signals": 93695, "successful_signals": 20594, "average_return": 0.015425572829531784, "success_probability": 0.21979828165857304, "sharpe_ratio": 0.3507459547836856, "weight": 0.034437176346281795}, "macd + obv": {"total_signals": 102670, "successful_signals": 20361, "average_return": 0.013886152254210915, "success_probability": 0.1983149897730593, "sharpe_ratio": 0.33976164158482647, "weight": 0.02797044692362526}, "macd + cmf": {"total_signals": 98366, "successful_signals": 20145, "average_return": 0.0139755036405297, "success_probability": 0.20479637273041498, "sharpe_ratio": 0.3440686864090824, "weight": 0.029070444282503036}, "adx + obv": {"total_signals": 94609, "successful_signals": 20832, "average_return": 0.01550468448855137, "success_probability": 0.2201904681372808, "sharpe_ratio": 0.37563985303129255, "weight": 0.03467555244770903}, "adx + cmf": {"total_signals": 73480, "successful_signals": 16582, "average_return": 0.015117734311084758, "success_probability": 0.22566684812193794, "sharpe_ratio": 0.3574080196314575, "weight": 0.03465105108607953}, "obv + cmf": {"total_signals": 84421, "successful_signals": 17201, "average_return": 0.013879091755305934, "success_probability": 0.20375262079340448, "sharpe_ratio": 0.35166634566871197, "weight": 0.028722761472793405}, "rsi + macd + adx": {"total_signals": 1178, "successful_signals": 278, "average_return": 0.014342528749753258, "success_probability": 0.23599320882852293, "sharpe_ratio": 0.4313617469726431, "weight": 0.03437851995091344}, "rsi + macd + obv": {"total_signals": 306, "successful_signals": 60, "average_return": 0.01431830487152884, "success_probability": 0.19607843137254902, "sharpe_ratio": 0.45663469541687274, "weight": 0.028515656226788564}, "rsi + macd + cmf": {"total_signals": 55, "successful_signals": 20, "average_return": 0.012326958344544165, "success_probability": 0.36363636363636365, "sharpe_ratio": 0.3164964411780838, "weight": 0.04552869222255028}, "rsi + adx + obv": {"total_signals": 4004, "successful_signals": 1261, "average_return": 0.021182448139441576, "success_probability": 0.31493506493506496, "sharpe_ratio": 0.6669013447250141, "weight": 0.06775777099220952}, "rsi + adx + cmf": {"total_signals": 665, "successful_signals": 184, "average_return": 0.01380392779590907, "success_probability": 0.27669172932330827, "sharpe_ratio": 0.44046805868881306, "weight": 0.03879366380665368}, "rsi + obv + cmf": {"total_signals": 174, "successful_signals": 71, "average_return": 0.024689253636375634, "success_probability": 0.40804597701149425, "sharpe_ratio": 0.8017871629010955, "weight": 0.1023243519563089}, "macd + adx + obv": {"total_signals": 52270, "successful_signals": 11205, "average_return": 0.015116671631661064, "success_probability": 0.21436770614118997, "sharpe_ratio": 0.3268777087867857, "weight": 0.03291375872558162}, "macd + adx + cmf": {"total_signals": 48956, "successful_signals": 11040, "average_return": 0.015547578371665004, "success_probability": 0.22550861998529292, "sharpe_ratio": 0.33251406714827614, "weight": 0.03561130123603072}, "macd + obv + cmf": {"total_signals": 59370, "successful_signals": 12112, "average_return": 0.013973637654851195, "success_probability": 0.2040087586323059, "sharpe_ratio": 0.32668576136604194, "weight": 0.028954777493478502}, "adx + obv + cmf": {"total_signals": 42141, "successful_signals": 9451, "average_return": 0.01535771267084888, "success_probability": 0.22427090007356257, "sharpe_ratio": 0.34038594029637226, "weight": 0.03498335081452636}}