import os
import zipfile
import pandas as pd
import numpy as np
import json
from itertools import combinations

# استيراد المؤشرات الفنية من مكتبة ta
from ta.momentum import RSIIndicator
from ta.trend import MACD, ADXIndicator
from ta.volume import OnBalanceVolumeIndicator, ChaikinMoneyFlowIndicator

DATA_FOLDER = "data"
ZIP_FILE = "data.zip"
OUTPUT_FILE = "realistic_final_weights.json"

# فك ضغط الملف إذا لم يكن مجلد البيانات موجودًا
if not os.path.exists(DATA_FOLDER):
    with zipfile.ZipFile(ZIP_FILE, 'r') as zip_ref:
        zip_ref.extractall(DATA_FOLDER)
    print("✅ تم فك ضغط data.zip")

# تعريف دوال الإشارات للمؤشرات الفنية
# ملاحظة: هذا يتطلب وجود بيانات كافية في DataFrame (عدة شموع سابقة)
signal_funcs = {
    'rsi': lambda df, i: (
        i >= 2 and
        35 <= df['rsi'].iloc[i] <= 65 and
        df['rsi'].iloc[i] > df['rsi'].iloc[i-1] > df['rsi'].iloc[i-2]
    ),
    'macd': lambda df, i: (
        i >= 1 and (
            (df['macd'].iloc[i] > df['macd_signal'].iloc[i] and df['macd'].iloc[i-1] <= df['macd_signal'].iloc[i-1]) or
            (df['macd'].iloc[i] > 0 and df['macd'].iloc[i] > df['macd'].iloc[i-1] and df['macd'].iloc[i] > df['macd_signal'].iloc[i])
        )
    ),
    'adx': lambda df, i: (
        i >= 1 and
        df['adx'].iloc[i] > 25 and
        df['adx'].iloc[i] > df['adx'].iloc[i-1]
    ),
    'obv': lambda df, i: (
        i >= 4 and
        all(df['obv'].iloc[j] < df['obv'].iloc[j+1] for j in range(i-3, i))
    ),
    'cmf': lambda df, i: (
        i >= 2 and
        all(val > 0.1 for val in df['cmf'].iloc[i-2:i+1].values)
    ),
}

# إعداد قواميس لتخزين الإحصائيات
stats = {}
returns_all = {}

for indicator in signal_funcs.keys():
    stats[indicator] = {
        'total_signals': 0,
        'successful_signals': 0,
        'average_return': 0,
        'success_probability': 0,
        'sharpe_ratio': 0,
        'weight': 0
    }
    returns_all[indicator] = []

# توليد جميع التركيبات الممكنة من 2 و 3 مؤشرات
all_combos = []
for r in [2, 3]:
    all_combos += list(combinations(signal_funcs.keys(), r))

# إعداد قواميس لتخزين إحصائيات التركيبات
combos_stats = {}
combo_returns_all = {}

for combo in all_combos:
    key = " + ".join(combo)
    combos_stats[key] = {
        'total_signals': 0,
        'successful_signals': 0,
        'average_return': 0,
        'success_probability': 0,
        'sharpe_ratio': 0,
        'weight': 0
    }
    combo_returns_all[key] = []

# قراءة وتحليل كل ملفات البيانات
print("🔍 جاري تحليل ملفات البيانات...")
processed_files = 0

# التحقق من وجود ملفات CSV
csv_files = [f for f in os.listdir(DATA_FOLDER) if f.endswith(".csv")]
print(f"📁 عدد ملفات CSV الموجودة: {len(csv_files)}")
if len(csv_files) == 0:
    print("⚠️ لا توجد ملفات CSV في المجلد. تأكد من وجود الملفات أو فك ضغط data.zip بشكل صحيح.")

for file in os.listdir(DATA_FOLDER):
    if not file.endswith(".csv"):
        continue

    try:
        print(f"📊 جاري تحليل {file}...")
        df = pd.read_csv(os.path.join(DATA_FOLDER, file))
        if df.shape[0] < 100:  # تجاهل الملفات ذات البيانات القليلة
            continue

        # تحويل الطابع الزمني وترتيب البيانات
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.sort_values('timestamp', inplace=True)
        df.set_index('timestamp', inplace=True)

        # حساب المؤشرات الفنية
        df['rsi'] = RSIIndicator(df['close']).rsi()
        macd = MACD(df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        adx_indicator = ADXIndicator(df['high'], df['low'], df['close'])
        df['adx'] = adx_indicator.adx()
        df['adx_pos'] = adx_indicator.adx_pos()
        df['adx_neg'] = adx_indicator.adx_neg()
        df['obv'] = OnBalanceVolumeIndicator(df['close'], df['volume']).on_balance_volume()
        df['obv_diff'] = df['obv'].diff()
        df['cmf'] = ChaikinMoneyFlowIndicator(df['high'], df['low'], df['close'], df['volume']).chaikin_money_flow()

        # حساب التغير السعري المستقبلي (3% خلال 3 شموع قادمة)
        df['future_max'] = df['close'].shift(-3).rolling(window=3).max()
        df['price_change'] = (df['future_max'] - df['close']) / df['close']

        # تحليل كل شمعة
        for i in range(len(df) - 3):
            row = df.iloc[i]
            if pd.isna(row['price_change']):
                continue

            # تحليل كل مؤشر على حدة
            for indicator, signal_func in signal_funcs.items():
                if signal_func(df, i):  # إذا كان المؤشر يعطي إشارة صعود
                    stats[indicator]['total_signals'] += 1
                    stats[indicator]['average_return'] += row['price_change']
                    returns_all[indicator].append(row['price_change'])
                    if row['price_change'] >= 0.03:  # إذا حدث صعود ≥ 3%
                        stats[indicator]['successful_signals'] += 1

            # تحليل تركيبات المؤشرات
            for combo in all_combos:
                if all(signal_funcs[s](df, i) for s in combo):  # إذا كانت جميع المؤشرات في التركيبة تعطي إشارة صعود
                    key = " + ".join(combo)
                    combos_stats[key]['total_signals'] += 1
                    combos_stats[key]['average_return'] += row['price_change']
                    combo_returns_all[key].append(row['price_change'])
                    if row['price_change'] >= 0.03:  # إذا حدث صعود ≥ 3%
                        combos_stats[key]['successful_signals'] += 1

        processed_files += 1
        if processed_files % 50 == 0:
            print(f"✓ تم تحليل {processed_files} ملف")

    except Exception as e:
        print(f"❌ خطأ في تحليل {file}: {str(e)}")

# حساب الإحصائيات النهائية للمؤشرات الفردية
print("📊 حساب الإحصائيات النهائية للمؤشرات...")
for indicator in signal_funcs.keys():
    s = stats[indicator]
    if s['total_signals'] == 0:
        continue
    s['success_probability'] = s['successful_signals'] / s['total_signals']
    s['average_return'] = s['average_return'] / s['total_signals']
    std_dev = np.std(returns_all[indicator]) if returns_all[indicator] else 1
    s['sharpe_ratio'] = s['average_return'] / std_dev if std_dev > 0 else 0
    s['weight'] = s['success_probability'] * s['average_return']

# حساب الإحصائيات النهائية للتركيبات
for key in combos_stats:
    s = combos_stats[key]
    if s['total_signals'] == 0:
        continue
    s['success_probability'] = s['successful_signals'] / s['total_signals']
    s['average_return'] = s['average_return'] / s['total_signals']
    std_dev = np.std(combo_returns_all[key]) if combo_returns_all[key] else 1
    s['sharpe_ratio'] = s['average_return'] / std_dev if std_dev > 0 else 0
    s['weight'] = s['success_probability'] * s['average_return']

# دمج نتائج المؤشرات الفردية والتركيبات
final_results = {**stats, **combos_stats}

# تطبيع الأوزان
total_weight = sum(item['weight'] for item in final_results.values() if item['total_signals'] > 0)
if total_weight > 0:
    for key in final_results:
        if final_results[key]['total_signals'] > 0:
            final_results[key]['weight'] /= total_weight

# حفظ النتائج في ملف JSON
with open(OUTPUT_FILE, 'w') as f:
    json.dump(final_results, f, indent=4, ensure_ascii=False)

# طباعة ملخص النتائج
print(f"✅ تم تحليل {processed_files} عملة وحساب أوزان {len(final_results)} مؤشر وتركيبة.")
print(f"✅ تم حفظ النتائج في {OUTPUT_FILE}")

# عرض أفضل 5 مؤشرات/تركيبات
top_indicators = sorted(
    [(k, v) for k, v in final_results.items() if v['total_signals'] >= 30],
    key=lambda x: x[1]['weight'],
    reverse=True
)[:5]

print("\n🏆 أفضل 5 مؤشرات/تركيبات:")
for i, (key, value) in enumerate(top_indicators, 1):
    print(f"{i}. {key}:")
    print(f"   - عدد الإشارات: {value['total_signals']}")
    print(f"   - نسبة النجاح: {value['success_probability']:.2%}")
    print(f"   - متوسط العائد: {value['average_return']:.2%}")
    print(f"   - نسبة شارب: {value['sharpe_ratio']:.4f}")
    print(f"   - الوزن: {value['weight']:.4f}")
