#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إشارات الخروج الذكي
يراقب العملات التي تم إرسال توصيات دخول لها
ويرسل إشارات خروج عند ظهور علامات ضعف أو هبوط 5%+
"""

import json
import os
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class ExitSignalsSystem:
    """نظام إشارات الخروج الذكي"""
    
    def __init__(self):
        self.recommendations_file = "recommendations_history.json"
        self.exit_signals_file = "exit_signals_history.json"
        self.monitored_positions_file = "monitored_positions.json"
        
        # إعدادات إشارات الخروج
        self.exit_threshold = -0.05  # -5% هبوط
        self.weakness_threshold = -0.03  # -3% علامات ضعف
        self.monitoring_period_hours = 72  # مدة المراقبة
        
        print("🚨 نظام إشارات الخروج الذكي جاهز")
        print("📉 عتبة الخروج: -5% هبوط")
        print("⚠️ عتبة الضعف: -3% علامات ضعف")
    
    def load_recommendations_history(self):
        """تحميل تاريخ التوصيات"""
        try:
            if os.path.exists(self.recommendations_file):
                with open(self.recommendations_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"❌ خطأ في تحميل تاريخ التوصيات: {e}")
            return []
    
    def load_exit_signals_history(self):
        """تحميل تاريخ إشارات الخروج"""
        try:
            if os.path.exists(self.exit_signals_file):
                with open(self.exit_signals_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            return []
    
    def save_exit_signals_history(self, history):
        """حفظ تاريخ إشارات الخروج"""
        try:
            with open(self.exit_signals_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطأ في حفظ تاريخ إشارات الخروج: {e}")
    
    def load_monitored_positions(self):
        """تحميل المراكز المراقبة"""
        try:
            if os.path.exists(self.monitored_positions_file):
                with open(self.monitored_positions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            return {}
    
    def save_monitored_positions(self, positions):
        """حفظ المراكز المراقبة"""
        try:
            with open(self.monitored_positions_file, 'w', encoding='utf-8') as f:
                json.dump(positions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطأ في حفظ المراكز المراقبة: {e}")
    
    def get_live_price(self, symbol):
        """الحصول على السعر الحي"""
        try:
            url = f"https://api.binance.com/api/v3/ticker/price"
            params = {'symbol': f"{symbol}USDT"}
            
            response = requests.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return float(data['price'])
            return None
        except Exception as e:
            return None
    
    def get_24h_change(self, symbol):
        """الحصول على التغير خلال 24 ساعة"""
        try:
            url = f"https://api.binance.com/api/v3/ticker/24hr"
            params = {'symbol': f"{symbol}USDT"}
            
            response = requests.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return float(data['priceChangePercent']) / 100
            return None
        except Exception as e:
            return None
    
    def analyze_weakness_signals(self, symbol, current_price, entry_price):
        """تحليل إشارات الضعف"""
        try:
            # حساب التغير من سعر الدخول
            price_change = (current_price - entry_price) / entry_price
            
            # الحصول على التغير خلال 24 ساعة
            change_24h = self.get_24h_change(symbol)
            
            weakness_signals = []
            
            # إشارة 1: هبوط من سعر الدخول
            if price_change <= self.weakness_threshold:
                weakness_signals.append(f"هبوط {price_change:.1%} من سعر الدخول")
            
            # إشارة 2: هبوط حاد خلال 24 ساعة
            if change_24h and change_24h <= -0.08:  # -8% خلال 24 ساعة
                weakness_signals.append(f"هبوط حاد {change_24h:.1%} خلال 24 ساعة")
            
            # إشارة 3: هبوط متتالي (يمكن إضافة المزيد من التحليل)
            if price_change <= self.exit_threshold:
                weakness_signals.append(f"وصول لعتبة الخروج {price_change:.1%}")
            
            return weakness_signals, price_change
            
        except Exception as e:
            return [], 0
    
    def update_monitored_positions(self):
        """تحديث المراكز المراقبة من التوصيات الحديثة"""
        try:
            recommendations = self.load_recommendations_history()
            monitored_positions = self.load_monitored_positions()
            
            current_time = datetime.now()
            
            # إضافة التوصيات الجديدة للمراقبة
            for rec in recommendations:
                symbol = rec['symbol']
                rec_time = datetime.fromisoformat(rec['timestamp'])
                
                # فقط التوصيات خلال فترة المراقبة
                if current_time - rec_time <= timedelta(hours=self.monitoring_period_hours):
                    if symbol not in monitored_positions:
                        monitored_positions[symbol] = {
                            'entry_price': rec['current_price'],
                            'entry_time': rec['timestamp'],
                            'target_price': rec['target_price'],
                            'target_return': rec.get('target_return', 0.08),
                            'probability': rec['probability'],
                            'exit_signal_sent': False
                        }
            
            # إزالة المراكز القديمة
            symbols_to_remove = []
            for symbol, position in monitored_positions.items():
                entry_time = datetime.fromisoformat(position['entry_time'])
                if current_time - entry_time > timedelta(hours=self.monitoring_period_hours):
                    symbols_to_remove.append(symbol)
            
            for symbol in symbols_to_remove:
                del monitored_positions[symbol]
            
            self.save_monitored_positions(monitored_positions)
            return monitored_positions
            
        except Exception as e:
            print(f"❌ خطأ في تحديث المراكز المراقبة: {e}")
            return {}
    
    def check_exit_signals(self):
        """فحص إشارات الخروج"""
        try:
            print("🔍 فحص إشارات الخروج...")
            
            monitored_positions = self.update_monitored_positions()
            exit_signals_history = self.load_exit_signals_history()
            
            new_exit_signals = []
            
            for symbol, position in monitored_positions.items():
                if position['exit_signal_sent']:
                    continue  # تم إرسال إشارة خروج مسبقاً
                
                print(f"📊 فحص {symbol}...")
                
                # الحصول على السعر الحي
                current_price = self.get_live_price(symbol)
                if not current_price:
                    continue
                
                entry_price = position['entry_price']
                
                # تحليل إشارات الضعف
                weakness_signals, price_change = self.analyze_weakness_signals(
                    symbol, current_price, entry_price
                )
                
                # تحديد نوع الإشارة
                signal_type = None
                if price_change <= self.exit_threshold:
                    signal_type = "خروج فوري"
                elif weakness_signals:
                    signal_type = "تحذير ضعف"
                
                if signal_type:
                    exit_signal = {
                        'symbol': symbol,
                        'signal_type': signal_type,
                        'entry_price': entry_price,
                        'current_price': current_price,
                        'price_change': price_change,
                        'weakness_signals': weakness_signals,
                        'entry_time': position['entry_time'],
                        'signal_time': datetime.now().isoformat(),
                        'target_price': position['target_price'],
                        'target_return': position['target_return']
                    }
                    
                    new_exit_signals.append(exit_signal)
                    exit_signals_history.append(exit_signal)
                    
                    # تحديث حالة الإرسال
                    if signal_type == "خروج فوري":
                        monitored_positions[symbol]['exit_signal_sent'] = True
                    
                    print(f"🚨 إشارة {signal_type} لـ {symbol}: {price_change:.1%}")
            
            # حفظ التحديثات
            self.save_exit_signals_history(exit_signals_history)
            self.save_monitored_positions(monitored_positions)
            
            return new_exit_signals
            
        except Exception as e:
            print(f"❌ خطأ في فحص إشارات الخروج: {e}")
            return []
    
    def format_exit_signal_message(self, exit_signal):
        """تنسيق رسالة إشارة الخروج"""
        try:
            signal_type = exit_signal['signal_type']
            symbol = exit_signal['symbol']
            price_change = exit_signal['price_change']
            
            if signal_type == "خروج فوري":
                emoji = "🚨"
                title = "إشارة خروج فورية"
                urgency = "عاجل"
            else:
                emoji = "⚠️"
                title = "تحذير ضعف"
                urgency = "تحذير"
            
            message = f"{emoji} <b>{title}</b>\n\n"
            message += f"🔴 <b>{urgency}</b> - #{symbol}\n"
            message += f"📉 <b>التغير:</b> {price_change:.1%}\n\n"
            
            message += f"💵 <b>سعر الدخول:</b> ${exit_signal['entry_price']:.4f}\n"
            message += f"💰 <b>السعر الحالي:</b> ${exit_signal['current_price']:.4f}\n"
            message += f"🎯 <b>السعر المستهدف:</b> ${exit_signal['target_price']:.4f}\n\n"
            
            if exit_signal['weakness_signals']:
                message += f"⚠️ <b>إشارات الضعف:</b>\n"
                for signal in exit_signal['weakness_signals']:
                    message += f"• {signal}\n"
                message += "\n"
            
            if signal_type == "خروج فوري":
                message += f"🚨 <b>توصية:</b> خروج فوري من المركز\n"
                message += f"📉 <b>السبب:</b> هبوط أكثر من 5%\n"
            else:
                message += f"⚠️ <b>توصية:</b> مراقبة دقيقة للمركز\n"
                message += f"📊 <b>السبب:</b> ظهور علامات ضعف\n"
            
            message += f"\n📅 <b>وقت الإشارة:</b> {datetime.fromisoformat(exit_signal['signal_time']).strftime('%Y-%m-%d %H:%M')}\n"
            message += f"⏰ <b>مدة المركز:</b> منذ {datetime.fromisoformat(exit_signal['entry_time']).strftime('%m-%d %H:%M')}\n\n"
            
            message += f"🛡️ <i>نظام الحماية الذكي | إدارة المخاطر</i>"
            
            return message.strip()
            
        except Exception as e:
            return f"❌ خطأ في تنسيق رسالة الخروج: {e}"
    
    def get_exit_signals_summary(self, exit_signals):
        """ملخص إشارات الخروج"""
        if not exit_signals:
            return "لا توجد إشارات خروج جديدة"
        
        exit_count = len([s for s in exit_signals if s['signal_type'] == "خروج فوري"])
        warning_count = len([s for s in exit_signals if s['signal_type'] == "تحذير ضعف"])
        
        summary = f"{len(exit_signals)} إشارة جديدة: "
        if exit_count > 0:
            summary += f"{exit_count} خروج فوري، "
        if warning_count > 0:
            summary += f"{warning_count} تحذير ضعف"
        
        return summary.rstrip("، ")

def main():
    """اختبار النظام"""
    exit_system = ExitSignalsSystem()
    
    # فحص إشارات الخروج
    exit_signals = exit_system.check_exit_signals()
    
    if exit_signals:
        print(f"\n🚨 تم اكتشاف {len(exit_signals)} إشارة خروج:")
        for signal in exit_signals:
            print(f"- {signal['symbol']}: {signal['signal_type']} ({signal['price_change']:.1%})")
            
            # عرض الرسالة المنسقة
            message = exit_system.format_exit_signal_message(signal)
            print(f"\n📱 رسالة التليجرام:\n{message}\n")
    else:
        print("✅ لا توجد إشارات خروج جديدة")

if __name__ == "__main__":
    main()
