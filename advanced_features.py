import pandas as pd
import numpy as np
import ta
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import joblib
import json
import os

class AdvancedCryptoAnalyzer:
    """محلل متقدم للعملات المشفرة مع تعلم آلي"""
    
    def __init__(self):
        self.model = None
        self.feature_names = []
        self.scaler = None
        
    def calculate_advanced_features(self, df):
        """حساب ميزات متقدمة للتحليل"""
        # المؤشرات الأساسية
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
        df['rsi_sma'] = df['rsi'].rolling(window=5).mean()
        
        # MACD متقدم
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        df['macd_histogram'] = macd.macd_diff()
        
        # EMAs متعددة
        for period in [9, 21, 50, 100, 200]:
            df[f'ema_{period}'] = ta.trend.EMAIndicator(close=df['close'], window=period).ema_indicator()
        
        # مؤشرات الزخم
        df['williams_r'] = ta.momentum.WilliamsRIndicator(high=df['high'], low=df['low'], close=df['close']).williams_r()
        df['cci'] = ta.trend.CCIIndicator(high=df['high'], low=df['low'], close=df['close']).cci()
        
        # مؤشرات الحجم المتقدمة
        df['mfi'] = ta.volume.MFIIndicator(high=df['high'], low=df['low'], close=df['close'], volume=df['volume']).money_flow_index()
        df['ad'] = ta.volume.AccDistIndexIndicator(high=df['high'], low=df['low'], close=df['close'], volume=df['volume']).acc_dist_index()
        
        # مؤشرات التقلبات
        df['atr'] = ta.volatility.AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()
        df['keltner_upper'] = ta.volatility.KeltnerChannel(high=df['high'], low=df['low'], close=df['close']).keltner_channel_hband()
        df['keltner_lower'] = ta.volatility.KeltnerChannel(high=df['high'], low=df['low'], close=df['close']).keltner_channel_lband()
        
        # مؤشرات الاتجاه
        df['adx'] = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close']).adx()
        df['di_plus'] = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close']).adx_pos()
        df['di_minus'] = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close']).adx_neg()
        
        # ميزات السعر
        df['price_change_1'] = df['close'].pct_change(1)
        df['price_change_3'] = df['close'].pct_change(3)
        df['price_change_7'] = df['close'].pct_change(7)
        
        # ميزات الحجم
        df['volume_sma_20'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma_20']
        df['volume_change'] = df['volume'].pct_change(1)
        
        # ميزات التقلبات
        df['volatility_20'] = df['close'].rolling(window=20).std()
        df['volatility_ratio'] = df['volatility_20'] / df['close']
        
        # مؤشرات الدعم والمقاومة
        df['high_20'] = df['high'].rolling(window=20).max()
        df['low_20'] = df['low'].rolling(window=20).min()
        df['price_position'] = (df['close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        
        # ميزات الشموع
        df['body_size'] = abs(df['close'] - df['open']) / df['open']
        df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['open']
        df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['open']
        
        return df
    
    def create_labels(self, df, future_periods=3, threshold=0.03):
        """إنشاء التصنيفات للتدريب"""
        labels = []
        
        for i in range(len(df) - future_periods):
            current_price = df['close'].iloc[i]
            future_price = df['close'].iloc[i + future_periods]
            price_change = (future_price - current_price) / current_price
            
            # 1 = صعود قوي، 0 = لا يوجد حركة مهمة
            label = 1 if price_change > threshold else 0
            labels.append(label)
        
        # إضافة قيم فارغة للفترات الأخيرة
        labels.extend([0] * future_periods)
        
        return labels
    
    def prepare_features(self, df):
        """تحضير الميزات للنموذج"""
        feature_columns = [
            'rsi', 'rsi_sma', 'macd', 'macd_signal', 'macd_histogram',
            'ema_9', 'ema_21', 'ema_50', 'ema_100', 'ema_200',
            'williams_r', 'cci', 'mfi', 'ad', 'atr',
            'adx', 'di_plus', 'di_minus',
            'price_change_1', 'price_change_3', 'price_change_7',
            'volume_ratio', 'volume_change',
            'volatility_ratio', 'price_position',
            'body_size', 'upper_shadow', 'lower_shadow'
        ]
        
        # التأكد من وجود الأعمدة
        available_features = [col for col in feature_columns if col in df.columns]
        
        return df[available_features].fillna(0), available_features
    
    def train_model(self, data_dir='data'):
        """تدريب نموذج التعلم الآلي"""
        print("🤖 بدء تدريب نموذج التعلم الآلي...")
        
        all_features = []
        all_labels = []
        
        files_processed = 0
        
        for filename in os.listdir(data_dir):
            if not filename.endswith('.csv'):
                continue
                
            try:
                filepath = os.path.join(data_dir, filename)
                df = pd.read_csv(filepath)
                
                if len(df) < 100:
                    continue
                
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
                df.sort_index(inplace=True)
                
                # حساب الميزات المتقدمة
                df = self.calculate_advanced_features(df)
                
                # إنشاء التصنيفات
                labels = self.create_labels(df)
                df['label'] = labels
                
                # تحضير الميزات
                features, feature_names = self.prepare_features(df)
                
                # إزالة الصفوف التي تحتوي على قيم فارغة
                valid_indices = ~(features.isnull().any(axis=1) | pd.Series(labels).isnull())
                
                if valid_indices.sum() > 50:  # التأكد من وجود بيانات كافية
                    all_features.append(features[valid_indices])
                    all_labels.extend(pd.Series(labels)[valid_indices].tolist())
                    
                    if not self.feature_names:
                        self.feature_names = feature_names
                
                files_processed += 1
                if files_processed % 20 == 0:
                    print(f"📊 تم معالجة {files_processed} ملف...")
                    
            except Exception as e:
                print(f"❌ خطأ في معالجة {filename}: {e}")
                continue
        
        if not all_features:
            print("❌ لا توجد بيانات كافية للتدريب")
            return False
        
        # دمج جميع البيانات
        X = pd.concat(all_features, ignore_index=True)
        y = np.array(all_labels)
        
        print(f"📊 إجمالي العينات: {len(X)}")
        print(f"📊 نسبة الحالات الإيجابية: {np.mean(y):.2%}")
        
        # تقسيم البيانات
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # تدريب النموذج
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1
        )
        
        print("🔄 جاري تدريب النموذج...")
        self.model.fit(X_train, y_train)
        
        # تقييم النموذج
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        print(f"✅ دقة التدريب: {train_score:.3f}")
        print(f"✅ دقة الاختبار: {test_score:.3f}")
        
        # تقرير مفصل
        y_pred = self.model.predict(X_test)
        print("\n📊 تقرير التصنيف:")
        print(classification_report(y_test, y_pred))
        
        # حفظ النموذج
        model_data = {
            'model': self.model,
            'feature_names': self.feature_names,
            'train_score': train_score,
            'test_score': test_score
        }
        
        joblib.dump(model_data, 'crypto_ml_model.pkl')
        print("💾 تم حفظ النموذج في crypto_ml_model.pkl")
        
        return True
    
    def load_model(self, model_path='crypto_ml_model.pkl'):
        """تحميل النموذج المدرب"""
        try:
            model_data = joblib.load(model_path)
            self.model = model_data['model']
            self.feature_names = model_data['feature_names']
            print("✅ تم تحميل النموذج بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج: {e}")
            return False
    
    def predict_symbol(self, df):
        """التنبؤ لعملة واحدة"""
        if self.model is None:
            return None, 0
        
        try:
            # حساب الميزات
            df = self.calculate_advanced_features(df)
            features, _ = self.prepare_features(df)
            
            if len(features) == 0:
                return None, 0
            
            # أخذ آخر صف للتنبؤ
            last_features = features.iloc[-1:][self.feature_names].fillna(0)
            
            # التنبؤ
            prediction = self.model.predict(last_features)[0]
            probability = self.model.predict_proba(last_features)[0][1]  # احتمالية الصعود
            
            return prediction, probability
            
        except Exception as e:
            print(f"❌ خطأ في التنبؤ: {e}")
            return None, 0
    
    def get_feature_importance(self):
        """الحصول على أهمية الميزات"""
        if self.model is None:
            return {}
        
        importance = dict(zip(self.feature_names, self.model.feature_importances_))
        return dict(sorted(importance.items(), key=lambda x: x[1], reverse=True))

def main():
    analyzer = AdvancedCryptoAnalyzer()
    
    # تدريب النموذج
    if analyzer.train_model():
        # عرض أهمية الميزات
        importance = analyzer.get_feature_importance()
        print("\n🔍 أهم الميزات:")
        for feature, score in list(importance.items())[:10]:
            print(f"   {feature}: {score:.3f}")

if __name__ == "__main__":
    main()
