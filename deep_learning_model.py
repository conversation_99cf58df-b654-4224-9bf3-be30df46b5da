#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج التعلم العميق متعدد الأطر الزمنية
النموذج: LSTM + Attention + Multi-Timeframe Fusion
الهدف: التنبؤ بالارتفاعات الكبيرة بدقة عالية
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, LSTM, Dense, Dropout, BatchNormalization, 
    Attention, Concatenate, Conv1D, GlobalMaxPooling1D,
    MultiHeadAttention, LayerNormalization
)
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.model_selection import train_test_split
import joblib
import os
import warnings
warnings.filterwarnings('ignore')

class MultiTimeframeCryptoPredictor:
    """نموذج التنبؤ متعدد الأطر الزمنية"""
    
    def __init__(self):
        self.model = None
        self.scalers = {
            '1h': RobustScaler(),
            '4h': RobustScaler(), 
            '1d': RobustScaler()
        }
        self.feature_columns = {
            '1h': ['close', 'volume', 'rsi', 'macd', 'rsi_fast', 'ema_fast', 'ema_slow', 'obv', 'cmf'],
            '4h': ['close', 'volume', 'rsi', 'macd', 'bb_upper', 'bb_lower', 'bb_middle', 'obv', 'cmf'],
            '1d': ['close', 'volume', 'rsi', 'macd', 'sma_50', 'sma_200', 'atr', 'obv', 'cmf']
        }
        self.sequence_lengths = {
            '1h': 24,   # آخر 24 ساعة
            '4h': 18,   # آخر 72 ساعة (18 * 4h)
            '1d': 14    # آخر 14 يوم
        }
    
    def create_advanced_features(self, df, timeframe):
        """إنشاء ميزات متقدمة"""
        df = df.copy()
        
        # ميزات السعر
        df['price_momentum'] = df['close'].pct_change(5)
        df['price_acceleration'] = df['price_momentum'].diff()
        df['high_low_ratio'] = df['high'] / df['low']
        
        # ميزات الحجم
        df['volume_momentum'] = df['volume'].pct_change(5)
        df['volume_price_trend'] = df['volume'] * df['close'].pct_change()
        
        # ميزات المؤشرات
        if 'rsi' in df.columns:
            df['rsi_momentum'] = df['rsi'].diff()
            df['rsi_divergence'] = df['rsi'].rolling(5).corr(df['close'])
        
        if 'macd' in df.columns:
            df['macd_momentum'] = df['macd'].diff()
            df['macd_strength'] = abs(df['macd'])
        
        # ميزات التقلبات
        df['volatility'] = df['close'].rolling(10).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(20).mean()
        
        # ميزات الاتجاه
        df['trend_strength'] = df['close'].rolling(10).apply(
            lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1
        )
        
        return df
    
    def prepare_sequences(self, df, timeframe, target_col='target'):
        """تحضير التسلسلات للنموذج"""
        # إنشاء الميزات المتقدمة
        df = self.create_advanced_features(df, timeframe)
        
        # اختيار الأعمدة المطلوبة
        feature_cols = self.feature_columns[timeframe]
        available_cols = [col for col in feature_cols if col in df.columns]
        
        if len(available_cols) < 5:
            return None, None
        
        # تنظيف البيانات
        df = df[available_cols + ([target_col] if target_col in df.columns else [])].dropna()
        
        if len(df) < self.sequence_lengths[timeframe] + 10:
            return None, None
        
        # تطبيع البيانات
        features_scaled = self.scalers[timeframe].fit_transform(df[available_cols])
        
        # إنشاء التسلسلات
        seq_length = self.sequence_lengths[timeframe]
        X, y = [], []
        
        for i in range(seq_length, len(features_scaled)):
            X.append(features_scaled[i-seq_length:i])
            if target_col in df.columns:
                y.append(df[target_col].iloc[i])
        
        return np.array(X), np.array(y) if y else None
    
    def build_model(self):
        """بناء النموذج متعدد الأطر الزمنية"""
        
        # مدخلات للأطر الزمنية المختلفة
        input_1h = Input(shape=(self.sequence_lengths['1h'], len(self.feature_columns['1h'])), name='input_1h')
        input_4h = Input(shape=(self.sequence_lengths['4h'], len(self.feature_columns['4h'])), name='input_4h')
        input_1d = Input(shape=(self.sequence_lengths['1d'], len(self.feature_columns['1d'])), name='input_1d')
        
        # معالجة كل إطار زمني بشكل منفصل
        def process_timeframe(input_layer, name_prefix):
            # طبقة تحويلية للاستخراج الأولي للميزات
            conv = Conv1D(64, 3, activation='relu', name=f'{name_prefix}_conv')(input_layer)
            conv = BatchNormalization(name=f'{name_prefix}_bn1')(conv)
            
            # LSTM مع Attention
            lstm_out = LSTM(128, return_sequences=True, name=f'{name_prefix}_lstm1')(conv)
            lstm_out = Dropout(0.3, name=f'{name_prefix}_dropout1')(lstm_out)
            
            # Multi-Head Attention
            attention_out = MultiHeadAttention(
                num_heads=8, key_dim=64, name=f'{name_prefix}_attention'
            )(lstm_out, lstm_out)
            attention_out = LayerNormalization(name=f'{name_prefix}_ln1')(attention_out)
            
            # LSTM ثاني
            lstm_out2 = LSTM(64, return_sequences=False, name=f'{name_prefix}_lstm2')(attention_out)
            lstm_out2 = BatchNormalization(name=f'{name_prefix}_bn2')(lstm_out2)
            lstm_out2 = Dropout(0.3, name=f'{name_prefix}_dropout2')(lstm_out2)
            
            return lstm_out2
        
        # معالجة كل إطار زمني
        processed_1h = process_timeframe(input_1h, '1h')
        processed_4h = process_timeframe(input_4h, '4h')
        processed_1d = process_timeframe(input_1d, '1d')
        
        # دمج الأطر الزمنية
        merged = Concatenate(name='merge_timeframes')([processed_1h, processed_4h, processed_1d])
        
        # طبقات التصنيف النهائية
        dense1 = Dense(256, activation='relu', name='dense1')(merged)
        dense1 = BatchNormalization(name='bn_final1')(dense1)
        dense1 = Dropout(0.4, name='dropout_final1')(dense1)
        
        dense2 = Dense(128, activation='relu', name='dense2')(dense1)
        dense2 = BatchNormalization(name='bn_final2')(dense2)
        dense2 = Dropout(0.3, name='dropout_final2')(dense2)
        
        dense3 = Dense(64, activation='relu', name='dense3')(dense2)
        dense3 = Dropout(0.2, name='dropout_final3')(dense3)
        
        # مخرجات متعددة
        prob_output = Dense(1, activation='sigmoid', name='probability')(dense3)  # احتمالية الارتفاع
        return_output = Dense(1, activation='linear', name='expected_return')(dense3)  # العائد المتوقع
        time_output = Dense(1, activation='relu', name='time_to_target')(dense3)  # الوقت للهدف
        
        # إنشاء النموذج
        model = Model(
            inputs=[input_1h, input_4h, input_1d],
            outputs=[prob_output, return_output, time_output],
            name='MultiTimeframeCryptoPredictor'
        )
        
        # تجميع النموذج
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss={
                'probability': 'binary_crossentropy',
                'expected_return': 'mse',
                'time_to_target': 'mse'
            },
            loss_weights={
                'probability': 1.0,
                'expected_return': 0.5,
                'time_to_target': 0.3
            },
            metrics={
                'probability': ['accuracy', 'precision', 'recall'],
                'expected_return': ['mae'],
                'time_to_target': ['mae']
            }
        )
        
        self.model = model
        return model
    
    def save_model(self, filepath='crypto_deep_model'):
        """حفظ النموذج والمعايرات"""
        if self.model:
            self.model.save(f'{filepath}.h5')
            joblib.dump(self.scalers, f'{filepath}_scalers.pkl')
            print(f"✅ تم حفظ النموذج: {filepath}")
    
    def load_model(self, filepath='crypto_deep_model'):
        """تحميل النموذج والمعايرات"""
        try:
            self.model = tf.keras.models.load_model(f'{filepath}.h5')
            self.scalers = joblib.load(f'{filepath}_scalers.pkl')
            print(f"✅ تم تحميل النموذج: {filepath}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج: {e}")
            return False
    
    def predict(self, data_1h, data_4h, data_1d):
        """التنبؤ باستخدام النموذج"""
        if not self.model:
            raise ValueError("النموذج غير محمل")
        
        # تحضير البيانات
        X_1h, _ = self.prepare_sequences(data_1h, '1h', target_col=None)
        X_4h, _ = self.prepare_sequences(data_4h, '4h', target_col=None)
        X_1d, _ = self.prepare_sequences(data_1d, '1d', target_col=None)
        
        if X_1h is None or X_4h is None or X_1d is None:
            return None
        
        # أخذ آخر تسلسل فقط للتنبؤ
        X_1h = X_1h[-1:] if len(X_1h) > 0 else None
        X_4h = X_4h[-1:] if len(X_4h) > 0 else None
        X_1d = X_1d[-1:] if len(X_1d) > 0 else None
        
        if X_1h is None or X_4h is None or X_1d is None:
            return None
        
        # التنبؤ
        predictions = self.model.predict([X_1h, X_4h, X_1d], verbose=0)
        
        return {
            'probability': float(predictions[0][0][0]),
            'expected_return': float(predictions[1][0][0]),
            'time_to_target': float(predictions[2][0][0])
        }

def create_model_instance():
    """إنشاء مثيل من النموذج"""
    predictor = MultiTimeframeCryptoPredictor()
    model = predictor.build_model()
    
    print("🧠 تم إنشاء نموذج التعلم العميق متعدد الأطر الزمنية")
    print(f"📊 المعاملات: {model.count_params():,}")
    print("🎯 المخرجات: احتمالية، عائد متوقع، وقت للهدف")
    
    return predictor

if __name__ == "__main__":
    # إنشاء واختبار النموذج
    predictor = create_model_instance()
    
    # عرض ملخص النموذج
    predictor.model.summary()
    
    # حفظ النموذج
    predictor.save_model('crypto_deep_model_v1')
