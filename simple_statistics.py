#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إحصائيات مبسط - 3 مؤشرات فقط
أفضل المؤشرات الأكثر اعتماداً عالمياً:
1. RSI - مؤشر القوة النسبية
2. MACD - مؤشر شامل للاتجاه والزخم  
3. OBV - تحليل الحجم (الأصدق)
"""

import os
import pandas as pd
import json
import numpy as np

# إعدادات
DATA_DIR = "data"
OUTPUT_FILE = "simple_weights.json"

# أفضل 3 مؤشرات فقط
INDICATORS = [
    'rsi_bullish',        # مؤشر القوة النسبية - الأكثر موثوقية
    'macd_bullish',       # مؤشر شامل للاتجاه والزخم
    'obv_bullish'         # تحليل الحجم - الأهم والأصدق
]

def detect_bullish_signals(df):
    """كشف الإشارات الصاعدة للمؤشرات الثلاثة فقط"""
    signals = dict.fromkeys(INDICATORS, False)

    # التأكد من وجود بيانات كافية
    if len(df) < 5:
        return signals

    try:
        # RSI Bullish: RSI بين 30-70 (منطقة صحية)
        if 'rsi' in df.columns:
            current_rsi = df['rsi'].iloc[-1]
            signals['rsi_bullish'] = 30 <= current_rsi <= 70

        # MACD Bullish: MACD فوق الإشارة ومتحسن
        if 'macd' in df.columns and 'macd_signal' in df.columns:
            current_macd = df['macd'].iloc[-1]
            current_signal = df['macd_signal'].iloc[-1]
            prev_macd = df['macd'].iloc[-2] if len(df) > 1 else current_macd
            prev_signal = df['macd_signal'].iloc[-2] if len(df) > 1 else current_signal
            
            signals['macd_bullish'] = (current_macd > current_signal and 
                                     (current_macd - current_signal) > (prev_macd - prev_signal))

        # OBV Bullish: OBV متزايد
        if 'obv' in df.columns:
            if len(df) >= 3:
                current_obv = df['obv'].iloc[-1]
                prev_obv = df['obv'].iloc[-2]
                prev2_obv = df['obv'].iloc[-3]
                signals['obv_bullish'] = current_obv > prev_obv > prev2_obv

    except Exception as e:
        print(f"⚠️ خطأ في كشف الإشارات: {e}")

    return signals

def analyze_file(file_path):
    """تحليل ملف واحد"""
    try:
        df = pd.read_csv(file_path)
        
        # التأكد من وجود الأعمدة المطلوبة
        required_columns = ['close', 'rsi', 'macd', 'macd_signal', 'obv']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"⚠️ أعمدة مفقودة في {file_path}: {missing_columns}")
            return {}

        results = {}
        
        for i in range(50, len(df) - 12):  # ترك مساحة للتحليل
            signals = detect_bullish_signals(df.iloc[:i+1])
            
            # البحث عن صعود في الـ 12 فترة القادمة
            current_price = df['close'].iloc[i]
            future_prices = df['close'].iloc[i+1:i+13]
            
            if len(future_prices) == 0:
                continue
                
            max_future_price = future_prices.max()
            price_change = (max_future_price - current_price) / current_price
            
            # معايير النجاح متدرجة
            for periods in range(1, min(13, len(future_prices) + 1)):
                future_price = df['close'].iloc[i + periods]
                period_change = (future_price - current_price) / current_price
                
                # معايير نجاح واقعية
                if periods <= 3:  # خلال 12 ساعة
                    success_threshold = 0.02  # 2%
                elif periods <= 6:  # خلال 24 ساعة
                    success_threshold = 0.04  # 4%
                else:  # خلال 48+ ساعة
                    success_threshold = 0.06  # 6%
                
                if period_change >= success_threshold:
                    for indicator, signal in signals.items():
                        if signal:
                            if indicator not in results:
                                results[indicator] = {
                                    'total_signals': 0,
                                    'successful_signals': 0,
                                    'returns': [],
                                    'time_to_targets': []
                                }
                            
                            results[indicator]['total_signals'] += 1
                            results[indicator]['successful_signals'] += 1
                            results[indicator]['returns'].append(period_change * 100)
                            results[indicator]['time_to_targets'].append(periods)
                    break
            else:
                # لم يحقق النجاح
                for indicator, signal in signals.items():
                    if signal:
                        if indicator not in results:
                            results[indicator] = {
                                'total_signals': 0,
                                'successful_signals': 0,
                                'returns': [],
                                'time_to_targets': []
                            }
                        results[indicator]['total_signals'] += 1
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في تحليل {file_path}: {e}")
        return {}

def main():
    print("🔍 بدء تحليل البيانات المبسط - 3 مؤشرات فقط...")
    print(f"📊 البحث عن ملفات CSV في مجلد: {DATA_DIR}")
    
    # التحقق من وجود مجلد البيانات
    if not os.path.exists(DATA_DIR):
        print(f"❌ مجلد البيانات غير موجود: {DATA_DIR}")
        return
    
    # عد ملفات CSV
    csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]
    print(f"📁 وجد {len(csv_files)} ملف CSV في المجلد")
    
    # إحصائيات شاملة
    all_stats = {indicator: {
        'total_signals': 0,
        'successful_signals': 0,
        'returns': [],
        'time_to_targets': []
    } for indicator in INDICATORS}
    
    files_processed = 0
    
    for filename in csv_files:
        file_path = os.path.join(DATA_DIR, filename)
        print(f"🔍 تحليل: {filename}")
        
        file_results = analyze_file(file_path)
        
        # دمج النتائج
        for indicator, stats in file_results.items():
            if indicator in all_stats:
                all_stats[indicator]['total_signals'] += stats['total_signals']
                all_stats[indicator]['successful_signals'] += stats['successful_signals']
                all_stats[indicator]['returns'].extend(stats['returns'])
                all_stats[indicator]['time_to_targets'].extend(stats['time_to_targets'])
        
        files_processed += 1
        
        if files_processed % 50 == 0:
            print(f"📈 تم تحليل {files_processed}/{len(csv_files)} ملف...")
    
    # حساب الإحصائيات النهائية
    final_weights = {}
    
    for indicator, stats in all_stats.items():
        if stats['total_signals'] > 0:
            success_rate = stats['successful_signals'] / stats['total_signals']
            avg_return = np.mean(stats['returns']) if stats['returns'] else 0
            avg_time = np.mean(stats['time_to_targets']) if stats['time_to_targets'] else 0
            
            # حساب نسبة شارب (العائد / المخاطر)
            returns_std = np.std(stats['returns']) if len(stats['returns']) > 1 else 1
            sharpe_ratio = avg_return / returns_std if returns_std > 0 else 0
            
            # حساب الوزن: احتمالية × عائد × جودة
            weight = success_rate * max(0, avg_return) * max(0, sharpe_ratio / 100)
            
            final_weights[indicator] = {
                'weight': weight,
                'success_probability': success_rate,
                'average_return': avg_return,
                'average_time_to_target': avg_time,
                'sharpe_ratio': sharpe_ratio,
                'total_signals': stats['total_signals'],
                'successful_signals': stats['successful_signals']
            }
    
    # تطبيع الأوزان
    total_weight = sum(v['weight'] for v in final_weights.values())
    if total_weight > 0:
        for indicator in final_weights:
            final_weights[indicator]['weight'] = final_weights[indicator]['weight'] / total_weight
    
    # حفظ النتائج
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(final_weights, f, indent=4, ensure_ascii=False)
    
    # عرض النتائج
    print(f"\n✅ تم تحليل {files_processed} ملف")
    print(f"📊 إحصائيات المؤشرات الثلاثة:")
    print("-" * 80)
    
    # ترتيب المؤشرات حسب الوزن
    sorted_indicators = sorted(final_weights.items(), key=lambda x: x[1]['weight'], reverse=True)
    
    for indicator, stats in sorted_indicators:
        print(f"\n🔹 {indicator}:")
        print(f"   الوزن: {stats['weight']:.4f}")
        print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
        print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
        print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
        print(f"   نسبة شارب: {stats['sharpe_ratio']:.3f}")
        print(f"   إجمالي الإشارات: {stats['total_signals']:,}")
        print(f"   الإشارات الناجحة: {stats['successful_signals']:,}")
    
    print(f"\n📊 ملخص:")
    print(f"   المؤشرات المقبولة: {len(final_weights)}/3")
    print(f"   إجمالي الأوزان: {sum(v['weight'] for v in final_weights.values()):.3f}")
    
    print(f"\n📁 تم حفظ الأوزان في {OUTPUT_FILE}")
    
    if len(final_weights) == 0:
        print("\n⚠️ تحذير: لم يتم قبول أي مؤشر! تحقق من البيانات والمعايير.")

if __name__ == "__main__":
    main()
