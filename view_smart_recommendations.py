#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
عارض التوصيات الذكية
يعرض التوصيات مع الأهداف المخصصة لكل عملة
"""

import json
from datetime import datetime

def load_recommendations():
    """تحميل التوصيات من الملف"""
    try:
        with open('recommendations_history.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ خطأ في تحميل التوصيات: {e}")
        return []

def analyze_targets():
    """تحليل الأهداف المختلفة"""
    recommendations = load_recommendations()
    
    if not recommendations:
        print("❌ لا توجد توصيات للعرض")
        return
    
    # تجميع البيانات
    data = []
    for rec in recommendations:
        target_return = rec.get('target_return', 0.08)  # افتراضي 8%
        data.append({
            'العملة': rec['symbol'],
            'الاحتمالية': f"{rec['probability']:.1%}",
            'الثقة': rec['confidence'],
            'السعر الحالي': f"${rec['current_price']:.4f}",
            'السعر المستهدف': f"${rec['target_price']:.4f}",
            'الهدف المخصص': f"{target_return:.1%}",
            'التغير 24h': f"{rec['price_change_24h']:+.2f}%",
            'الوقت': datetime.fromisoformat(rec['timestamp']).strftime('%H:%M')
        })
    
    # ترتيب حسب الاحتمالية
    data.sort(key=lambda x: float(x['الاحتمالية'].replace('%', '')), reverse=True)
    
    return data

def show_target_distribution():
    """عرض توزيع الأهداف"""
    recommendations = load_recommendations()
    
    if not recommendations:
        return
    
    targets = {}
    for rec in recommendations:
        target = rec.get('target_return', 0.08)
        target_range = f"{target:.1%}"
        
        if target_range not in targets:
            targets[target_range] = []
        targets[target_range].append(rec['symbol'])
    
    print("\n📊 توزيع الأهداف المخصصة:")
    print("=" * 50)
    
    for target_range, symbols in sorted(targets.items()):
        print(f"🎯 {target_range}: {len(symbols)} عملة")
        print(f"   العملات: {', '.join(symbols[:10])}")
        if len(symbols) > 10:
            print(f"   ... و {len(symbols) - 10} عملة أخرى")
        print()

def show_top_recommendations(limit=20):
    """عرض أفضل التوصيات"""
    data = analyze_targets()
    
    if not data:
        return
    
    print(f"\n🔥 أفضل {limit} توصية (مرتبة حسب الاحتمالية):")
    print("=" * 120)

    # عرض التوصيات
    for i, rec in enumerate(data[:limit], 1):
        print(f"\n{i:2d}. 💰 {rec['العملة']} | 📊 {rec['الاحتمالية']} | 🎯 {rec['الهدف المخصص']} | 💵 {rec['السعر الحالي']} → {rec['السعر المستهدف']} | ⏰ {rec['الوقت']}")

def show_volatility_analysis():
    """تحليل التقلبات والأهداف"""
    recommendations = load_recommendations()
    
    if not recommendations:
        return
    
    # تجميع البيانات حسب نوع العملة
    large_caps = []
    medium_caps = []
    small_caps = []
    
    large_cap_symbols = ['BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'SOL', 'DOT']
    medium_cap_symbols = ['MATIC', 'LINK', 'UNI', 'AVAX', 'ATOM', 'NEAR', 'ICP']
    
    for rec in recommendations:
        symbol = rec['symbol']
        target = rec.get('target_return', 0.08)
        
        if symbol in large_cap_symbols:
            large_caps.append(target)
        elif symbol in medium_cap_symbols:
            medium_caps.append(target)
        else:
            small_caps.append(target)
    
    print("\n📈 تحليل الأهداف حسب حجم العملة:")
    print("=" * 50)
    
    if large_caps:
        avg_large = sum(large_caps) / len(large_caps)
        print(f"🏦 العملات الكبيرة ({len(large_caps)} عملة): متوسط الهدف {avg_large:.1%}")
        print(f"   نطاق: {min(large_caps):.1%} - {max(large_caps):.1%}")
    
    if medium_caps:
        avg_medium = sum(medium_caps) / len(medium_caps)
        print(f"🏢 العملات المتوسطة ({len(medium_caps)} عملة): متوسط الهدف {avg_medium:.1%}")
        print(f"   نطاق: {min(medium_caps):.1%} - {max(medium_caps):.1%}")
    
    if small_caps:
        avg_small = sum(small_caps) / len(small_caps)
        print(f"🚀 العملات الصغيرة ({len(small_caps)} عملة): متوسط الهدف {avg_small:.1%}")
        print(f"   نطاق: {min(small_caps):.1%} - {max(small_caps):.1%}")

def show_confidence_analysis():
    """تحليل مستويات الثقة"""
    recommendations = load_recommendations()
    
    if not recommendations:
        return
    
    confidence_levels = {}
    for rec in recommendations:
        confidence = rec['confidence']
        if confidence not in confidence_levels:
            confidence_levels[confidence] = []
        confidence_levels[confidence].append(rec)
    
    print("\n🎯 تحليل مستويات الثقة:")
    print("=" * 50)
    
    for level, recs in confidence_levels.items():
        avg_target = sum(rec.get('target_return', 0.08) for rec in recs) / len(recs)
        avg_prob = sum(rec['probability'] for rec in recs) / len(recs)
        
        print(f"📊 {level}: {len(recs)} توصية")
        print(f"   متوسط الاحتمالية: {avg_prob:.1%}")
        print(f"   متوسط الهدف: {avg_target:.1%}")
        print()

def main():
    """الوظيفة الرئيسية"""
    print("🎯 عارض التوصيات الذكية")
    print("=" * 50)
    
    recommendations = load_recommendations()
    
    if not recommendations:
        print("❌ لا توجد توصيات للعرض")
        return
    
    print(f"📊 إجمالي التوصيات: {len(recommendations)}")
    print(f"⏰ آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # عرض التحليلات
    show_top_recommendations(15)
    show_target_distribution()
    show_volatility_analysis()
    show_confidence_analysis()
    
    # إحصائيات عامة
    targets = [rec.get('target_return', 0.08) for rec in recommendations]
    probabilities = [rec['probability'] for rec in recommendations]
    
    print("\n📈 إحصائيات عامة:")
    print("=" * 30)
    print(f"🎯 متوسط الأهداف: {sum(targets)/len(targets):.1%}")
    print(f"📊 أعلى هدف: {max(targets):.1%}")
    print(f"📉 أقل هدف: {min(targets):.1%}")
    print(f"⚡ متوسط الاحتمالية: {sum(probabilities)/len(probabilities):.1%}")
    print(f"🔥 أعلى احتمالية: {max(probabilities):.1%}")
    
    print("\n✅ تم عرض جميع التحليلات بنجاح!")

if __name__ == "__main__":
    main()
