#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير التوصيات الذكية المجمعة
يجمع أفضل 5 توصيات ويرسلها في رسالة واحدة
يتجاهل التوصيات المرسلة سابقاً إلا عند التغيير الكبير
"""

import json
import os
from datetime import datetime, timedelta
from typing import List, Dict

class SmartRecommendationsManager:
    """مدير التوصيات الذكية"""
    
    def __init__(self):
        self.sent_recommendations_file = "sent_recommendations_cache.json"
        self.change_threshold = 0.15  # 15% تغيير في الاحتمالية أو الهدف
        
    def load_sent_cache(self):
        """تحميل كاش التوصيات المرسلة"""
        try:
            if os.path.exists(self.sent_recommendations_file):
                with open(self.sent_recommendations_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            return {}
    
    def save_sent_cache(self, cache):
        """حفظ كاش التوصيات المرسلة"""
        try:
            with open(self.sent_recommendations_file, 'w', encoding='utf-8') as f:
                json.dump(cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطأ في حفظ الكاش: {e}")
    
    def has_significant_change(self, old_rec, new_rec):
        """فحص وجود تغيير كبير في التوصية"""
        try:
            # تغيير في الاحتمالية
            prob_change = abs(new_rec['probability'] - old_rec['probability'])
            
            # تغيير في الهدف
            old_target = old_rec.get('target_return', 0.08)
            new_target = new_rec.get('target_return', 0.08)
            target_change = abs(new_target - old_target) / old_target if old_target > 0 else 0
            
            # تغيير في السعر (أكثر من 5%)
            price_change = abs(new_rec['current_price'] - old_rec['current_price']) / old_rec['current_price']
            
            return (prob_change > self.change_threshold or 
                   target_change > 0.10 or 
                   price_change > 0.05)
            
        except Exception as e:
            return True  # في حالة الخطأ، اعتبر أن هناك تغيير
    
    def should_include_recommendation(self, recommendation, sent_cache):
        """تحديد ما إذا كان يجب تضمين التوصية"""
        symbol = recommendation['symbol']
        
        # إذا لم ترسل من قبل
        if symbol not in sent_cache:
            return True
        
        # فحص التغيير الكبير
        old_rec = sent_cache[symbol]
        return self.has_significant_change(old_rec, recommendation)
    
    def filter_recommendations(self, recommendations):
        """فلترة التوصيات حسب الإرسال السابق"""
        sent_cache = self.load_sent_cache()
        filtered = []
        
        for rec in recommendations:
            if self.should_include_recommendation(rec, sent_cache):
                filtered.append(rec)
        
        return filtered
    
    def select_top_recommendations(self, recommendations, count=5):
        """اختيار أفضل التوصيات"""
        if not recommendations:
            return []
        
        # فلترة التوصيات المرسلة سابقاً
        filtered_recs = self.filter_recommendations(recommendations)
        
        if not filtered_recs:
            print("📋 جميع التوصيات تم إرسالها سابقاً بدون تغييرات كبيرة")
            return []
        
        # ترتيب حسب الاحتمالية والعائد المتوقع
        def score_recommendation(rec):
            probability = rec['probability']
            target_return = rec.get('target_return', 0.08)
            # نقاط مركبة: 70% احتمالية + 30% عائد
            return probability * 0.7 + target_return * 0.3
        
        # ترتيب حسب النقاط
        sorted_recs = sorted(filtered_recs, key=score_recommendation, reverse=True)
        
        # أخذ أفضل 5
        top_recs = sorted_recs[:count]
        
        # تحديث الكاش
        self.update_sent_cache(top_recs)
        
        return top_recs
    
    def update_sent_cache(self, recommendations):
        """تحديث كاش التوصيات المرسلة"""
        sent_cache = self.load_sent_cache()
        
        for rec in recommendations:
            sent_cache[rec['symbol']] = {
                'probability': rec['probability'],
                'target_return': rec.get('target_return', 0.08),
                'current_price': rec['current_price'],
                'timestamp': rec['timestamp']
            }
        
        self.save_sent_cache(sent_cache)
    
    def format_combined_message(self, recommendations, stats):
        """تنسيق رسالة مجمعة لأفضل التوصيات"""
        if not recommendations:
            return None
        
        # ترتيب حسب الاحتمالية
        sorted_recs = sorted(recommendations, key=lambda x: x['probability'], reverse=True)
        
        message = "🔥 <b>أفضل 5 توصيات من النظام المتقدم</b>\n\n"
        
        for i, rec in enumerate(sorted_recs, 1):
            confidence_emoji = "🔥" if rec['probability'] > 0.9 else "⚡" if rec['probability'] > 0.8 else "📊"
            target_return = rec.get('target_return', 0.08)
            
            message += f"{confidence_emoji} <b>{i}. #{rec['symbol']}</b>\n"
            message += f"📊 <b>الاحتمالية:</b> {rec['probability']:.1%} | "
            message += f"🎯 <b>الهدف:</b> +{target_return:.1%}\n"
            message += f"💵 <b>السعر:</b> ${rec['current_price']:.4f} → ${rec['target_price']:.4f}\n"
            message += f"📈 <b>التغير 24h:</b> {rec['price_change_24h']:+.2f}%\n"
            message += f"⏰ <b>مدة التتبع:</b> 72 ساعة\n\n"
        
        # إحصائيات النظام
        message += "📊 <b>إحصائيات النظام:</b>\n"
        message += f"✅ <b>معدل النجاح:</b> {stats.get('success_rate', 0):.1%}\n"
        message += f"📈 <b>إجمالي التوصيات:</b> {stats.get('total_recommendations', 0)}\n"
        message += f"🎯 <b>التوصيات الناجحة:</b> {stats.get('successful_recommendations', 0)}\n\n"
        
        # معلومات إضافية
        avg_probability = sum(rec['probability'] for rec in sorted_recs) / len(sorted_recs)
        avg_target = sum(rec.get('target_return', 0.08) for rec in sorted_recs) / len(sorted_recs)
        
        message += f"⚡ <b>متوسط الاحتمالية:</b> {avg_probability:.1%}\n"
        message += f"🎯 <b>متوسط الأهداف:</b> {avg_target:.1%}\n"
        message += f"📅 <b>وقت التحليل:</b> {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n"
        
        message += "🧠 <i>تحليل ذكي مخصص لكل عملة | بيانات حية</i>\n"
        message += "🎯 <i>أفضل 5 توصيات من التحليل الشامل</i>"
        
        return message.strip()
    
    def get_recommendation_summary(self, recommendations):
        """ملخص سريع للتوصيات"""
        if not recommendations:
            return "لا توجد توصيات جديدة"
        
        count = len(recommendations)
        avg_prob = sum(rec['probability'] for rec in recommendations) / count
        avg_target = sum(rec.get('target_return', 0.08) for rec in recommendations) / count
        
        symbols = [rec['symbol'] for rec in recommendations]
        
        return f"{count} توصيات جديدة | متوسط الاحتمالية: {avg_prob:.1%} | متوسط الهدف: {avg_target:.1%} | العملات: {', '.join(symbols)}"

def main():
    """اختبار النظام"""
    manager = SmartRecommendationsManager()
    
    # محاكاة توصيات للاختبار
    test_recommendations = [
        {
            'symbol': 'BTC',
            'probability': 0.85,
            'target_return': 0.05,
            'current_price': 45000,
            'target_price': 47250,
            'price_change_24h': 2.5,
            'timestamp': datetime.now().isoformat()
        },
        {
            'symbol': 'ETH',
            'probability': 0.92,
            'target_return': 0.08,
            'current_price': 3000,
            'target_price': 3240,
            'price_change_24h': 3.2,
            'timestamp': datetime.now().isoformat()
        }
    ]
    
    # اختبار الاختيار
    top_recs = manager.select_top_recommendations(test_recommendations)
    
    print("🎯 أفضل التوصيات:")
    for rec in top_recs:
        print(f"- {rec['symbol']}: {rec['probability']:.1%}")
    
    # اختبار التنسيق
    stats = {'success_rate': 0.85, 'total_recommendations': 100, 'successful_recommendations': 85}
    message = manager.format_combined_message(top_recs, stats)
    
    if message:
        print("\n📱 رسالة التليجرام:")
        print(message)

if __name__ == "__main__":
    main()
