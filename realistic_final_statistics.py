#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إحصائيات واقعي نهائي - أفضل ما هو متاح
الاستراتيجية الواقعية النهائية:
- أخذ أفضل المؤشرات المتاحة (بغض النظر عن العتبات المثالية)
- ترتيب المؤشرات حسب الأداء الفعلي
- تطبيق أوزان ذكية نسبية

المؤشرات الأساسية (متوفرة في جميع الملفات):
1. RSI - كشف البيع/الشراء المفرط (⭐⭐⭐⭐)
2. MACD - إشارات الموجات المتوسطة (⭐⭐⭐⭐)  
3. OBV - تدفق السيولة الحقيقية (⭐⭐⭐⭐)

المؤشرات التكميلية (إذا متوفرة):
4. ADX - قوة الاتجاه (⭐⭐⭐⭐)
5. CMF - فلترة الصعود الكاذب (⭐⭐⭐)
"""

import os
import pandas as pd
import json
import numpy as np

# إعدادات
DATA_DIR = "data"
OUTPUT_FILE = "realistic_final_weights.json"

# المؤشرات الواقعية
INDICATORS = [
    'rsi_realistic',        # RSI واقعي
    'macd_realistic',       # MACD واقعي
    'obv_realistic',        # OBV واقعي
    'adx_realistic',        # ADX واقعي (إذا متوفر)
    'cmf_realistic'         # CMF واقعي (إذا متوفر)
]

def detect_realistic_signals(df):
    """كشف الإشارات الواقعية"""
    signals = dict.fromkeys(INDICATORS, False)

    # التأكد من وجود بيانات كافية
    if len(df) < 10:
        return signals

    try:
        # === RSI الواقعي: أي تحسن في RSI ===
        if 'rsi' in df.columns:
            current_rsi = df['rsi'].iloc[-1]
            prev_rsi = df['rsi'].iloc[-2] if len(df) > 1 else current_rsi
            
            # RSI متحسن ولا يزيد عن 80 (تجنب الشراء المفرط)
            signals['rsi_realistic'] = (
                current_rsi > prev_rsi and   # تحسن
                current_rsi <= 80            # ليس مفرط الشراء
            )

        # === MACD الواقعي: أي تحسن في MACD ===
        if 'macd' in df.columns and 'macd_signal' in df.columns:
            current_macd = df['macd'].iloc[-1]
            current_signal = df['macd_signal'].iloc[-1]
            prev_macd = df['macd'].iloc[-2] if len(df) > 1 else current_macd
            
            # MACD متحسن أو فوق الإشارة
            signals['macd_realistic'] = (
                current_macd > prev_macd or      # تحسن
                current_macd > current_signal    # فوق الإشارة
            )

        # === OBV الواقعي: أي تحسن في الحجم ===
        if 'obv' in df.columns:
            if len(df) >= 3:
                current_obv = df['obv'].iloc[-1]
                prev_obv = df['obv'].iloc[-2]
                
                # OBV متزايد
                signals['obv_realistic'] = current_obv > prev_obv

        # === ADX الواقعي: أي قوة اتجاه (إذا متوفر) ===
        if all(col in df.columns for col in ['adx', 'adx_pos', 'adx_neg']):
            current_adx = df['adx'].iloc[-1]
            current_pos = df['adx_pos'].iloc[-1]
            current_neg = df['adx_neg'].iloc[-1]
            
            # أي قوة اتجاه صاعد
            signals['adx_realistic'] = (
                current_adx > 15 and           # قوة اتجاه أساسية
                current_pos > current_neg      # اتجاه صاعد
            )

        # === CMF الواقعي: أي تدفق إيجابي (إذا متوفر) ===
        if 'cmf' in df.columns:
            current_cmf = df['cmf'].iloc[-1]
            
            # أي تدفق نقدي إيجابي
            signals['cmf_realistic'] = current_cmf > 0

    except Exception as e:
        print(f"⚠️ خطأ في كشف الإشارات الواقعية: {e}")

    return signals

def analyze_file_realistic(file_path):
    """تحليل ملف واحد بالاستراتيجية الواقعية"""
    try:
        df = pd.read_csv(file_path)
        
        # التأكد من وجود الأعمدة الأساسية
        required_columns = ['close', 'rsi', 'macd', 'macd_signal', 'obv']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return {}

        results = {}
        
        for i in range(20, len(df) - 12):  # ترك مساحة للتحليل
            signals = detect_realistic_signals(df.iloc[:i+1])
            
            # البحث عن أي صعود في الـ 12 فترة القادمة
            current_price = df['close'].iloc[i]
            future_prices = df['close'].iloc[i+1:i+13]
            
            if len(future_prices) == 0:
                continue
                
            max_future_price = future_prices.max()
            max_price_change = (max_future_price - current_price) / current_price
            
            # معايير نجاح واقعية جداً
            for periods in range(1, min(13, len(future_prices) + 1)):
                future_price = df['close'].iloc[i + periods]
                period_change = (future_price - current_price) / current_price
                
                # عتبات واقعية للصعود
                if periods <= 3:  # خلال 12 ساعة
                    success_threshold = 0.02  # 2%
                elif periods <= 6:  # خلال 24 ساعة
                    success_threshold = 0.03  # 3%
                else:  # خلال 48+ ساعة
                    success_threshold = 0.04  # 4%
                
                if period_change >= success_threshold:
                    for indicator, signal in signals.items():
                        if signal:
                            if indicator not in results:
                                results[indicator] = {
                                    'total_signals': 0,
                                    'successful_signals': 0,
                                    'returns': [],
                                    'time_to_targets': []
                                }
                            
                            results[indicator]['total_signals'] += 1
                            results[indicator]['successful_signals'] += 1
                            results[indicator]['returns'].append(period_change * 100)
                            results[indicator]['time_to_targets'].append(periods)
                    break
            else:
                # لم يحقق النجاح
                for indicator, signal in signals.items():
                    if signal:
                        if indicator not in results:
                            results[indicator] = {
                                'total_signals': 0,
                                'successful_signals': 0,
                                'returns': [],
                                'time_to_targets': []
                            }
                        results[indicator]['total_signals'] += 1
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في تحليل {file_path}: {e}")
        return {}

def main():
    print("🎯 بدء التحليل الواقعي النهائي - أفضل ما هو متاح...")
    print("📊 بدون عتبات مثالية - أخذ أفضل المؤشرات المتاحة")
    print(f"📁 البحث عن ملفات CSV في مجلد: {DATA_DIR}")
    
    # التحقق من وجود مجلد البيانات
    if not os.path.exists(DATA_DIR):
        print(f"❌ مجلد البيانات غير موجود: {DATA_DIR}")
        return
    
    # عد ملفات CSV
    csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]
    print(f"📁 وجد {len(csv_files)} ملف CSV في المجلد")
    
    # إحصائيات شاملة
    all_stats = {indicator: {
        'total_signals': 0,
        'successful_signals': 0,
        'returns': [],
        'time_to_targets': []
    } for indicator in INDICATORS}
    
    files_processed = 0
    
    for filename in csv_files:
        file_path = os.path.join(DATA_DIR, filename)
        
        file_results = analyze_file_realistic(file_path)
        
        # دمج النتائج
        for indicator, stats in file_results.items():
            if indicator in all_stats:
                all_stats[indicator]['total_signals'] += stats['total_signals']
                all_stats[indicator]['successful_signals'] += stats['successful_signals']
                all_stats[indicator]['returns'].extend(stats['returns'])
                all_stats[indicator]['time_to_targets'].extend(stats['time_to_targets'])
        
        files_processed += 1
        
        if files_processed % 50 == 0:
            print(f"📈 تم تحليل {files_processed}/{len(csv_files)} ملف...")
    
    # حساب الإحصائيات النهائية - أخذ أفضل ما هو متاح
    final_weights = {}
    
    for indicator, stats in all_stats.items():
        if stats['total_signals'] > 10:  # حد أدنى منخفض جداً
            success_rate = stats['successful_signals'] / stats['total_signals']
            avg_return = np.mean(stats['returns']) if stats['returns'] else 0
            avg_time = np.mean(stats['time_to_targets']) if stats['time_to_targets'] else 0
            
            # أخذ أي مؤشر له أداء إيجابي
            if success_rate > 0.20 and avg_return > 1.0:  # عتبات منخفضة جداً
                # حساب نسبة شارب (العائد / المخاطر)
                returns_std = np.std(stats['returns']) if len(stats['returns']) > 1 else 1
                sharpe_ratio = avg_return / returns_std if returns_std > 0 else 0
                
                # حساب الوزن: احتمالية × عائد × استقرار
                weight = success_rate * avg_return * max(0.1, min(1.0, sharpe_ratio / 10))
                
                final_weights[indicator] = {
                    'weight': weight,
                    'success_probability': success_rate,
                    'average_return': avg_return,
                    'average_time_to_target': avg_time,
                    'sharpe_ratio': sharpe_ratio,
                    'total_signals': stats['total_signals'],
                    'successful_signals': stats['successful_signals']
                }
    
    # تطبيع الأوزان
    total_weight = sum(v['weight'] for v in final_weights.values())
    if total_weight > 0:
        for indicator in final_weights:
            final_weights[indicator]['weight'] = final_weights[indicator]['weight'] / total_weight
    
    # حفظ النتائج
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(final_weights, f, indent=4, ensure_ascii=False)
    
    # عرض النتائج
    print(f"\n✅ تم تحليل {files_processed} ملف")
    print(f"🎯 إحصائيات المؤشرات الواقعية النهائية:")
    print("-" * 80)
    
    if final_weights:
        # ترتيب المؤشرات حسب الوزن
        sorted_indicators = sorted(final_weights.items(), key=lambda x: x[1]['weight'], reverse=True)
        
        for indicator, stats in sorted_indicators:
            print(f"\n🔹 {indicator}:")
            print(f"   الوزن: {stats['weight']:.4f}")
            print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
            print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
            print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
            print(f"   نسبة شارب: {stats['sharpe_ratio']:.3f}")
            print(f"   إجمالي الإشارات: {stats['total_signals']:,}")
            print(f"   الإشارات الناجحة: {stats['successful_signals']:,}")
        
        print(f"\n📊 ملخص:")
        print(f"   المؤشرات المقبولة: {len(final_weights)}/5")
        print(f"   إجمالي الأوزان: {sum(v['weight'] for v in final_weights.values()):.3f}")
        
        # حساب متوسط الأداء العام
        avg_success = np.mean([v['success_probability'] for v in final_weights.values()])
        avg_return = np.mean([v['average_return'] for v in final_weights.values()])
        
        print(f"\n🎯 الأداء العام:")
        print(f"   متوسط احتمالية النجاح: {avg_success:.1%}")
        print(f"   متوسط العائد المتوقع: {avg_return:+.2f}%")
        
    else:
        print("\n⚠️ لا توجد مؤشرات تحقق حتى العتبات المنخفضة!")
        print("   العتبات: احتمالية >20%, عائد >1%")
    
    print(f"\n📁 تم حفظ الأوزان الواقعية النهائية في {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
