# 🚀 النظام المتكامل للعملات المشفرة

## 📋 نظرة عامة

النظام المتكامل للعملات المشفرة هو نظام ذكي متقدم يستخدم التعلم العميق لتحليل العملات المشفرة وتوليد توصيات عالية الجودة.

### 🎯 المميزات الرئيسية

- **🧠 نموذج ذكي متقدم**: مدرب على 379 عملة و 115K+ عينة
- **📊 دقة عالية**: 90% دقة في التنبؤ بارتفاعات 8%+
- **📱 تليجرام تلقائي**: إرسال التوصيات مباشرة للمجموعة
- **📈 تتبع الأداء**: تقييم تلقائي لنجاح التوصيات
- **⚡ سرعة فائقة**: تحليل 30+ عملة في 5 ثوانٍ
- **🔄 تشغيل مستمر**: جدولة تلقائية للتحليل

## 📁 هيكل المشروع

```
crypto_system/
├── run_crypto_system.py          # ملف التشغيل الرئيسي
├── integrated_crypto_system.py   # النظام المتكامل
├── config.py                     # ملف الإعدادات
├── simple_crypto_model.h5        # النموذج المدرب
├── simple_crypto_model_scaler.pkl # معايرات البيانات
├── multi_timeframe_data/         # قاعدة البيانات
├── requirements.txt              # المكتبات المطلوبة
├── README.md                     # هذا الملف
├── recommendations_history.json  # تاريخ التوصيات
└── performance_stats.json        # إحصائيات الأداء
```

## 🛠️ التثبيت والإعداد

### 1. تثبيت المكتبات المطلوبة

```bash
pip install -r requirements.txt
```

### 2. إعداد التليجرام

1. أنشئ بوت جديد عبر [@BotFather](https://t.me/BotFather)
2. احصل على Bot Token
3. أضف البوت إلى مجموعتك
4. احصل على Chat ID للمجموعة
5. حدث ملف `config.py`:

```python
TELEGRAM_CONFIG = {
    "bot_token": "YOUR_BOT_TOKEN_HERE",
    "chat_id": "YOUR_CHAT_ID_HERE"
}
```

## 🚀 التشغيل

### التشغيل التفاعلي

```bash
python run_crypto_system.py
```

### خيارات التشغيل

1. **تحليل فوري**: تشغيل تحليل واحد فوري
2. **تشغيل مستمر**: جدولة تلقائية كل 4/6/12 ساعة
3. **عرض الإحصائيات**: مراجعة أداء النظام
4. **تحديث التليجرام**: تغيير إعدادات البوت

## 📊 كيفية عمل النظام

### 1. جمع البيانات
- جلب بيانات أفضل 30 عملة من Binance
- حساب 6 مؤشرات فنية متقدمة
- تحضير تسلسلات زمنية للنموذج

### 2. التحليل والتنبؤ
- استخدام نموذج LSTM متقدم
- تحليل متوازي لـ 30+ عملة
- فلترة النتائج حسب مستوى الثقة

### 3. إرسال التوصيات
- تنسيق رسائل احترافية
- إرسال تلقائي للتليجرام
- تسجيل التوصيات للتتبع

### 4. تتبع الأداء
- مراقبة النتائج لمدة 72 ساعة
- تحديث إحصائيات النجاح
- عرض معدل الأداء في كل توصية

## 📈 مثال على التوصية

```
🔥 توصية جديدة من النظام المتقدم

💰 العملة: #EDU
📊 الاحتمالية: 93.3%
🎯 مستوى الثقة: عالية

💵 السعر الحالي: $0.1519
🎯 السعر المستهدف: $0.1641
📈 الهدف: +8.0%

📊 التغير 24h: +7.05%
📊 الحجم 24h: $310,845,716

⏰ مدة التتبع: 72 ساعة
📅 وقت التحليل: 2025-08-26 20:04

📊 إحصائيات النظام:
✅ معدل النجاح: 85.2%
📈 إجمالي التوصيات: 127
🎯 التوصيات الناجحة: 108

🧠 مدرب على 379 عملة و 115K+ عينة | دقة 90%
```

## ⚙️ الإعدادات المتقدمة

### ملف config.py

```python
# إعدادات النموذج
MODEL_CONFIG = {
    "min_confidence": 0.70,    # الحد الأدنى للثقة
    "target_return": 0.08,     # الهدف المطلوب (8%)
    "tracking_hours": 72       # مدة التتبع بالساعات
}

# إعدادات التحليل
ANALYSIS_CONFIG = {
    "max_workers": 10,         # عدد الخيوط المتوازية
    "max_cryptos": 30,         # عدد العملات للتحليل
    "timeframe": "4h"          # الإطار الزمني
}
```

## 📊 مراقبة الأداء

### ملفات السجلات
- `crypto_system.log`: سجل النظام
- `recommendations_history.json`: تاريخ التوصيات
- `performance_stats.json`: إحصائيات الأداء

### مؤشرات الأداء
- **معدل النجاح**: نسبة التوصيات التي وصلت للهدف
- **متوسط الاحتمالية**: متوسط ثقة النموذج
- **وقت التحليل**: سرعة معالجة البيانات

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في تحميل النموذج**
   - تأكد من وجود ملفات النموذج
   - تحقق من إصدار TensorFlow

2. **فشل إرسال التليجرام**
   - تحقق من Bot Token
   - تأكد من Chat ID الصحيح
   - تحقق من اتصال الإنترنت

3. **بطء في التحليل**
   - قلل عدد العملات في الإعدادات
   - قلل عدد الخيوط المتوازية

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- راجع ملف السجلات `crypto_system.log`
- تحقق من إعدادات `config.py`
- تأكد من تثبيت جميع المكتبات المطلوبة

## 📄 الترخيص

هذا المشروع مطور للاستخدام الشخصي والتعليمي.

---

**⚠️ تنبيه**: التداول في العملات المشفرة ينطوي على مخاطر عالية. استخدم النظام للمساعدة في اتخاذ القرارات وليس كنصيحة استثمارية نهائية.
