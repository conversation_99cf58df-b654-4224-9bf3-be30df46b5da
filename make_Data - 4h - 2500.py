import ccxt
import pandas as pd
import os
from datetime import datetime
import ta  # مكتبة المؤشرات الفنية

# إعدادات التحميل
TIMEFRAME = '4h'
LIMIT = 2500
DATA_DIR = 'data'

def load_symbols(filename="symbols.txt"):
    if not os.path.exists(filename):
        print(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                # تحويل من SYMBOL/USDT إلى SYMBOLUSDT لأسماء الملفات
                symbol_for_file = line.replace("/", "")
                symbols.append((line, symbol_for_file))  # (للتداول, لاسم الملف)
        return symbols

def fetch_ohlcv(symbol, timeframe=TIMEFRAME, limit=LIMIT):
    exchange = ccxt.binance()
    print(f"⬇️ تحميل بيانات {symbol} ({timeframe}, {limit} شمعة)...")
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)
    return df

def calculate_indicators(df):
    # RSI
    df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()

    # MACD
    macd = ta.trend.MACD(close=df['close'])
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()

    # EMA
    df['ema20'] = ta.trend.EMAIndicator(close=df['close'], window=20).ema_indicator()
    df['ema50'] = ta.trend.EMAIndicator(close=df['close'], window=50).ema_indicator()

    # ADX
    adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
    df['adx'] = adx.adx()

    # Bollinger Bands
    bb = ta.volatility.BollingerBands(close=df['close'])
    df['bb_upper'] = bb.bollinger_hband()
    df['bb_lower'] = bb.bollinger_lband()

    # OBV
    df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()

    # CMF (Chaikin Money Flow)
    df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
        high=df['high'],
        low=df['low'],
        close=df['close'],
        volume=df['volume'],
        window=20
    ).chaikin_money_flow()

    # Stochastic RSI
    stoch_rsi = ta.momentum.StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
    df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
    df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()

    # Volume Spike (مقارنة بالحجم المتوسط لـ20 شمعة)
    df['volume_avg_20'] = df['volume'].rolling(window=20).mean()
    df['volume_spike'] = df['volume'] / df['volume_avg_20']

    return df

def fetch_and_save_with_indicators(trading_symbol, file_symbol):
    try:
        # استخدام trading_symbol للتداول مع Binance
        df = fetch_ohlcv(trading_symbol)
        df = calculate_indicators(df)
        # استخدام file_symbol لاسم الملف
        output_path = os.path.join(DATA_DIR, f"{file_symbol}.csv")
        df.to_csv(output_path)
        print(f"✅ تم حفظ البيانات مع المؤشرات: {output_path}")
    except Exception as e:
        print(f"❌ خطأ في {trading_symbol}: {e}")

def main():
    symbols = load_symbols()
    if not symbols:
        return

    if not os.path.exists(DATA_DIR):
        os.makedirs(DATA_DIR)

    print(f"📊 سيتم تحميل بيانات {len(symbols)} عملة...")

    for i, (trading_symbol, file_symbol) in enumerate(symbols, 1):
        print(f"[{i}/{len(symbols)}] معالجة {trading_symbol}")
        fetch_and_save_with_indicators(trading_symbol, file_symbol)

if __name__ == "__main__":
    main()
