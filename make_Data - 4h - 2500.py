import ccxt
import pandas as pd
import os
from datetime import datetime
import ta  # مكتبة المؤشرات الفنية

# إعدادات التحميل
TIMEFRAME = '4h'
LIMIT = 2500
DATA_DIR = 'data'

def load_symbols(filename="symbols.txt"):
    if not os.path.exists(filename):
        print(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                # تحويل من SYMBOL/USDT إلى SYMBOLUSDT لأسماء الملفات
                symbol_for_file = line.replace("/", "")
                symbols.append((line, symbol_for_file))  # (للتداول, لاسم الملف)
        return symbols

def fetch_ohlcv(symbol, timeframe=TIMEFRAME, limit=LIMIT):
    exchange = ccxt.binance()
    print(f"⬇️ تحميل بيانات {symbol} ({timeframe}, {limit} شمعة)...")
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)
    return df

def calculate_indicators(df):
    # RSI
    df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()

    # MACD
    macd = ta.trend.MACD(close=df['close'])
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()

    # OBV - تحليل الحجم (الأهم والأصدق)
    df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()

    # ADX (Average Directional Index) - قوة الاتجاه
    adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'], window=14)
    df['adx'] = adx.adx()
    df['adx_pos'] = adx.adx_pos()
    df['adx_neg'] = adx.adx_neg()

    # CMF (Chaikin Money Flow) - قوة السيولة
    df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
        high=df['high'],
        low=df['low'],
        close=df['close'],
        volume=df['volume'],
        window=20
    ).chaikin_money_flow()

    # ===== أفضل 5 مؤشرات عالية الاعتماد =====
    # RSI + MACD + OBV + ADX + CMF = الاستراتيجية الذكية

    return df

def analyze_big_moves(df):
    """تحليل الحركات الكبيرة وتحديد الإشارات التي سبقتها"""
    big_moves = []

    for i in range(50, len(df) - 12):  # ترك مساحة للتحليل
        current_price = df['close'].iloc[i]

        # البحث عن ارتفاعات كبيرة في الـ 12 فترة القادمة
        future_prices = df['close'].iloc[i+1:i+13]
        if len(future_prices) == 0:
            continue

        max_future_price = future_prices.max()
        price_change = (max_future_price - current_price) / current_price

        # التركيز على الارتفاعات الكبيرة فقط (15%+)
        if price_change >= 0.15:  # 15% أو أكثر
            # تحديد متى حدث الارتفاع
            for periods in range(1, min(13, len(future_prices) + 1)):
                future_price = df['close'].iloc[i + periods]
                period_change = (future_price - current_price) / current_price

                if period_change >= 0.15:  # وصل للهدف
                    big_moves.append({
                        'index': i,
                        'price_change': period_change,
                        'periods_to_target': periods,
                        'timestamp': df.index[i]
                    })
                    break

    return big_moves

def fetch_and_save_with_indicators(trading_symbol, file_symbol):
    try:
        # استخدام trading_symbol للتداول مع Binance
        df = fetch_ohlcv(trading_symbol)
        df = calculate_indicators(df)

        # تحليل الحركات الكبيرة
        big_moves = analyze_big_moves(df)

        if len(big_moves) > 0:
            print(f"🎯 {trading_symbol}: وجد {len(big_moves)} ارتفاع كبير (15%+)")
            # استخدام file_symbol لاسم الملف
            output_path = os.path.join(DATA_DIR, f"{file_symbol}.csv")
            df.to_csv(output_path)
            print(f"✅ تم حفظ البيانات مع المؤشرات: {output_path}")
        else:
            print(f"⚠️ {trading_symbol}: لا توجد ارتفاعات كبيرة - تم تجاهله")

    except Exception as e:
        print(f"❌ خطأ في {trading_symbol}: {e}")

def main():
    symbols = load_symbols()
    if not symbols:
        return

    if not os.path.exists(DATA_DIR):
        os.makedirs(DATA_DIR)

    print(f"📊 سيتم تحميل بيانات {len(symbols)} عملة...")

    for i, (trading_symbol, file_symbol) in enumerate(symbols, 1):
        print(f"[{i}/{len(symbols)}] معالجة {trading_symbol}")
        fetch_and_save_with_indicators(trading_symbol, file_symbol)

if __name__ == "__main__":
    main()
