import pandas as pd
import json
import os
from datetime import datetime

def test_weights_loading():
    """اختبار تحميل الأوزان"""
    try:
        with open('weights.json', 'r') as f:
            weights = json.load(f)
        print("✅ تم تحميل الأوزان بنجاح:")
        for indicator, weight in weights.items():
            print(f"   {indicator}: {weight}")
        return True
    except Exception as e:
        print(f"❌ خطأ في تحميل الأوزان: {e}")
        return False

def test_data_loading():
    """اختبار تحميل البيانات"""
    data_dir = 'data'
    if not os.path.exists(data_dir):
        print("❌ مجلد البيانات غير موجود")
        return False
    
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    print(f"📊 تم العثور على {len(csv_files)} ملف بيانات")
    
    if csv_files:
        # اختبار ملف واحد
        test_file = os.path.join(data_dir, csv_files[0])
        try:
            df = pd.read_csv(test_file)
            print(f"✅ تم تحميل {csv_files[0]} بنجاح")
            print(f"   الأعمدة: {list(df.columns)}")
            print(f"   عدد الصفوف: {len(df)}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            return False
    else:
        print("❌ لا توجد ملفات بيانات")
        return False

def test_symbols_loading():
    """اختبار تحميل قائمة العملات"""
    try:
        with open('symbols.txt', 'r') as f:
            symbols = [line.strip().replace("/", "") for line in f if line.strip()]
        print(f"✅ تم تحميل {len(symbols)} عملة")
        print(f"   أول 5 عملات: {symbols[:5]}")
        return True
    except Exception as e:
        print(f"❌ خطأ في تحميل العملات: {e}")
        return False

def test_enhanced_monitor():
    """اختبار النظام المحسن"""
    try:
        # محاولة استيراد الوحدات
        import ccxt
        import ta
        print("✅ تم تحميل المكتبات المطلوبة")
        
        # اختبار الاتصال بـ Binance
        exchange = ccxt.binance()
        ticker = exchange.fetch_ticker('BTCUSDT')
        print(f"✅ الاتصال بـ Binance ناجح - سعر BTC: ${ticker['last']}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def main():
    print("🧪 اختبار سريع للنظام")
    print("=" * 40)
    
    tests = [
        ("تحميل الأوزان", test_weights_loading),
        ("تحميل البيانات", test_data_loading),
        ("تحميل العملات", test_symbols_loading),
        ("النظام المحسن", test_enhanced_monitor)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للعمل")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه")

if __name__ == "__main__":
    main()
