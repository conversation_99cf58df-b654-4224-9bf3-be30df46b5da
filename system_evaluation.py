import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
# import matplotlib.pyplot as plt
# import seaborn as sns

class CryptoSystemEvaluator:
    """فئة تقييم نظام التنبؤ بالعملات المشفرة"""
    
    def __init__(self, data_dir='data', weights_file='weights.json'):
        self.data_dir = data_dir
        self.weights_file = weights_file
        self.weights = self.load_weights()
        self.results = {}
        
    def load_weights(self):
        """تحميل أوزان المؤشرات"""
        try:
            with open(self.weights_file, 'r') as f:
                return json.load(f)
        except:
            return {}
    
    def calculate_indicators(self, df):
        """حساب المؤشرات الفنية"""
        # نفس المؤشرات المستخدمة في النظام الأصلي
        import ta
        
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
        
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        
        df['ema20'] = ta.trend.EMAIndicator(close=df['close'], window=20).ema_indicator()
        df['ema50'] = ta.trend.EMAIndicator(close=df['close'], window=50).ema_indicator()
        
        adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        df['adx'] = adx.adx()
        
        bb = ta.volatility.BollingerBands(close=df['close'])
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_lower'] = bb.bollinger_lband()
        
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'], low=df['low'], close=df['close'], volume=df['volume']
        ).chaikin_money_flow()
        
        stoch_rsi = ta.momentum.StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
        df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
        df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
        
        df['volume_avg_20'] = df['volume'].rolling(window=20).mean()
        df['volume_spike_ratio'] = df['volume'] / df['volume_avg_20']
        
        return df
    
    def detect_signals(self, df, index):
        """كشف الإشارات في نقطة زمنية محددة"""
        signals = {}
        
        try:
            # RSI
            signals['rsi_bullish'] = 30 <= df['rsi'].iloc[index] <= 70
            
            # MACD
            signals['macd_bullish'] = df['macd'].iloc[index] > df['macd_signal'].iloc[index]
            
            # ADX
            signals['adx_bullish'] = df['adx'].iloc[index] > 25
            
            # Bollinger
            signals['bollinger_breakout'] = df['close'].iloc[index] > df['bb_upper'].iloc[index]
            
            # EMA
            signals['ema20_above_ema50'] = df['ema20'].iloc[index] > df['ema50'].iloc[index]
            
            # Volume
            signals['volume_spike'] = df['volume_spike_ratio'].iloc[index] > 1.5
            
            # OBV
            if index >= 5:
                signals['obv_bullish'] = df['obv'].iloc[index] > df['obv'].iloc[index-5]
            else:
                signals['obv_bullish'] = False
            
            # CMF
            signals['cmf_bullish'] = df['cmf'].iloc[index] > 0
            
            # Stochastic RSI
            k = df['stoch_rsi_k'].iloc[index]
            d = df['stoch_rsi_d'].iloc[index]
            signals['stoch_rsi_bullish'] = k > d and k < 0.8
            
            return signals
        except:
            return {}
    
    def calculate_score(self, signals):
        """حساب النقاط باستخدام الأوزان"""
        total_score = 0
        for signal_name, is_active in signals.items():
            if is_active and signal_name in self.weights:
                total_score += self.weights[signal_name]
        return total_score
    
    def evaluate_file(self, filepath, future_periods=[1, 3, 6, 12]):
        """تقييم ملف واحد"""
        try:
            df = pd.read_csv(filepath)
            if len(df) < 100:
                return None
            
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # حساب المؤشرات إذا لم تكن موجودة
            if 'rsi' not in df.columns:
                df = self.calculate_indicators(df)
            
            df.dropna(inplace=True)
            
            results = {
                'symbol': os.path.basename(filepath).replace('.csv', ''),
                'total_signals': 0,
                'successful_predictions': {p: 0 for p in future_periods},
                'total_predictions': {p: 0 for p in future_periods},
                'accuracy': {p: 0 for p in future_periods},
                'avg_return': {p: 0 for p in future_periods},
                'max_return': {p: 0 for p in future_periods},
                'min_return': {p: 0 for p in future_periods}
            }
            
            returns_data = {p: [] for p in future_periods}
            
            # تقييم الإشارات
            for i in range(50, len(df) - max(future_periods)):
                signals = self.detect_signals(df, i)
                score = self.calculate_score(signals)
                
                # إذا كانت النقاط أعلى من العتبة
                if score >= 0.6:
                    results['total_signals'] += 1
                    current_price = df['close'].iloc[i]
                    
                    # فحص النتائج للفترات المختلفة
                    for periods in future_periods:
                        if i + periods < len(df):
                            future_price = df['close'].iloc[i + periods]
                            return_pct = (future_price - current_price) / current_price * 100
                            
                            returns_data[periods].append(return_pct)
                            results['total_predictions'][periods] += 1
                            
                            # اعتبار التنبؤ ناجحاً إذا كان العائد > 2%
                            if return_pct > 2:
                                results['successful_predictions'][periods] += 1
            
            # حساب الإحصائيات
            for periods in future_periods:
                if results['total_predictions'][periods] > 0:
                    results['accuracy'][periods] = results['successful_predictions'][periods] / results['total_predictions'][periods]
                    
                    if returns_data[periods]:
                        results['avg_return'][periods] = np.mean(returns_data[periods])
                        results['max_return'][periods] = np.max(returns_data[periods])
                        results['min_return'][periods] = np.min(returns_data[periods])
            
            return results
            
        except Exception as e:
            print(f"خطأ في تقييم {filepath}: {e}")
            return None
    
    def evaluate_system(self):
        """تقييم النظام بالكامل"""
        print("🔍 بدء تقييم النظام...")
        
        all_results = []
        processed_files = 0
        
        for filename in os.listdir(self.data_dir):
            if filename.endswith('.csv'):
                filepath = os.path.join(self.data_dir, filename)
                result = self.evaluate_file(filepath)
                
                if result:
                    all_results.append(result)
                    processed_files += 1
                    
                    if processed_files % 20 == 0:
                        print(f"📊 تم معالجة {processed_files} ملف...")
        
        if not all_results:
            print("❌ لا توجد نتائج للتقييم")
            return
        
        # تجميع النتائج
        summary = self.generate_summary(all_results)
        
        # حفظ النتائج
        self.save_results(all_results, summary)
        
        # عرض التقرير
        self.print_report(summary)
        
        return summary
    
    def generate_summary(self, results):
        """إنشاء ملخص النتائج"""
        summary = {
            'total_files': len(results),
            'total_signals': sum(r['total_signals'] for r in results),
            'periods_analysis': {}
        }
        
        for periods in [1, 3, 6, 12]:
            total_predictions = sum(r['total_predictions'][periods] for r in results)
            successful_predictions = sum(r['successful_predictions'][periods] for r in results)
            
            if total_predictions > 0:
                accuracy = successful_predictions / total_predictions
                avg_returns = [r['avg_return'][periods] for r in results if r['total_predictions'][periods] > 0]
                
                summary['periods_analysis'][periods] = {
                    'total_predictions': total_predictions,
                    'successful_predictions': successful_predictions,
                    'accuracy': accuracy,
                    'avg_return': np.mean(avg_returns) if avg_returns else 0,
                    'median_return': np.median(avg_returns) if avg_returns else 0
                }
        
        return summary
    
    def save_results(self, results, summary):
        """حفظ النتائج"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # حفظ النتائج التفصيلية
        with open(f'evaluation_results_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump({
                'summary': summary,
                'detailed_results': results,
                'evaluation_date': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
        
        print(f"💾 تم حفظ النتائج في evaluation_results_{timestamp}.json")
    
    def print_report(self, summary):
        """طباعة تقرير التقييم"""
        print("\n" + "="*60)
        print("📊 تقرير تقييم نظام التنبؤ بالعملات المشفرة")
        print("="*60)
        
        print(f"📁 إجمالي الملفات المعالجة: {summary['total_files']}")
        print(f"🚨 إجمالي الإشارات المرسلة: {summary['total_signals']}")
        
        print("\n📈 تحليل الدقة حسب الفترة الزمنية:")
        print("-" * 50)
        
        for periods, data in summary['periods_analysis'].items():
            hours = periods * 4  # كل فترة = 4 ساعات
            print(f"\n⏰ خلال {hours} ساعة ({periods} فترات):")
            print(f"   📊 إجمالي التنبؤات: {data['total_predictions']}")
            print(f"   ✅ التنبؤات الناجحة: {data['successful_predictions']}")
            print(f"   🎯 معدل الدقة: {data['accuracy']:.2%}")
            print(f"   💰 متوسط العائد: {data['avg_return']:.2f}%")
            print(f"   📊 متوسط العائد (وسيط): {data['median_return']:.2f}%")
        
        print("\n" + "="*60)

def main():
    evaluator = CryptoSystemEvaluator()
    evaluator.evaluate_system()

if __name__ == "__main__":
    main()
