#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام البيانات الحية للعملات المشفرة
يعتمد على مصادر متعددة: Binance + CoinGecko + مصادر أخرى
تحديث البيانات كل ساعة + أسعار لحظية
"""

import os
import json
import pandas as pd
import numpy as np
import requests
import time
from datetime import datetime, timedelta
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

class LiveDataManager:
    """مدير البيانات الحية"""
    
    def __init__(self):
        self.data_dir = "live_crypto_data"
        self.price_cache = {}
        self.last_update = {}
        self.update_interval = 3600  # ساعة واحدة
        self.price_update_interval = 60  # دقيقة واحدة للأسعار
        
        # إنشاء مجلد البيانات
        os.makedirs(self.data_dir, exist_ok=True)
        
        print("🔄 نظام البيانات الحية جاهز")
        print("📊 مصادر البيانات: Binance + CoinGecko")
        print("⏰ تحديث البيانات: كل ساعة")
        print("💰 تحديث الأسعار: كل دقيقة")
    
    def get_live_price_binance(self, symbol):
        """جلب السعر الحي من Binance"""
        try:
            url = f"https://api.binance.com/api/v3/ticker/price"
            params = {'symbol': f"{symbol}USDT"}
            
            response = requests.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return float(data['price'])
            return None
            
        except Exception as e:
            return None
    
    def get_live_price_coingecko(self, symbol):
        """جلب السعر الحي من CoinGecko"""
        try:
            # تحويل رمز العملة لـ CoinGecko
            symbol_map = {
                'BTC': 'bitcoin', 'ETH': 'ethereum', 'BNB': 'binancecoin',
                'ADA': 'cardano', 'SOL': 'solana', 'XRP': 'ripple',
                'DOT': 'polkadot', 'DOGE': 'dogecoin', 'AVAX': 'avalanche-2',
                'MATIC': 'matic-network', 'LINK': 'chainlink', 'UNI': 'uniswap'
            }
            
            coin_id = symbol_map.get(symbol, symbol.lower())
            url = f"https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': coin_id,
                'vs_currencies': 'usd'
            }
            
            response = requests.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if coin_id in data:
                    return data[coin_id]['usd']
            return None
            
        except Exception as e:
            return None
    
    def get_best_live_price(self, symbol):
        """الحصول على أفضل سعر حي من مصادر متعددة"""
        prices = []
        
        # جلب من Binance
        binance_price = self.get_live_price_binance(symbol)
        if binance_price:
            prices.append(binance_price)
        
        # جلب من CoinGecko
        coingecko_price = self.get_live_price_coingecko(symbol)
        if coingecko_price:
            prices.append(coingecko_price)
        
        if prices:
            # إذا كان هناك أكثر من سعر، نأخذ المتوسط
            if len(prices) > 1:
                return sum(prices) / len(prices)
            else:
                return prices[0]
        
        return None
    
    def get_historical_data_binance(self, symbol, timeframe='4h', limit=200):
        """جلب البيانات التاريخية من Binance"""
        try:
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': f"{symbol}USDT",
                'interval': timeframe,
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code != 200:
                return None
            
            data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # تحويل الأنواع
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df[['open', 'high', 'low', 'close', 'volume']]
            
        except Exception as e:
            return None
    
    def calculate_indicators(self, df):
        """حساب المؤشرات الفنية"""
        try:
            if len(df) < 20:
                return None
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            df['macd'] = exp1 - exp2
            
            # OBV
            df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
            
            # CMF
            mfm = ((df['close'] - df['low']) - (df['high'] - df['close'])) / (df['high'] - df['low'])
            mfm = mfm.fillna(0)
            mfv = mfm * df['volume']
            df['cmf'] = mfv.rolling(window=20).sum() / df['volume'].rolling(window=20).sum()
            
            return df
            
        except Exception as e:
            return None
    
    def update_crypto_data(self, symbol):
        """تحديث بيانات عملة واحدة"""
        try:
            print(f"🔄 تحديث بيانات {symbol}...")
            
            # جلب البيانات التاريخية
            df = self.get_historical_data_binance(symbol)
            if df is None:
                return False
            
            # حساب المؤشرات
            df = self.calculate_indicators(df)
            if df is None:
                return False
            
            # جلب السعر الحي الحالي
            live_price = self.get_best_live_price(symbol)
            if live_price:
                # إضافة السعر الحي كآخر نقطة
                current_time = datetime.now()
                last_row = df.iloc[-1].copy()
                last_row['close'] = live_price
                last_row.name = current_time
                
                # إضافة الصف الجديد
                df = pd.concat([df, last_row.to_frame().T])
            
            # حفظ البيانات
            file_path = os.path.join(self.data_dir, f"{symbol}_live.csv")
            df.to_csv(file_path)
            
            # تحديث الكاش
            self.price_cache[symbol] = live_price if live_price else df['close'].iloc[-1]
            self.last_update[symbol] = datetime.now()
            
            print(f"✅ تم تحديث {symbol} - السعر: ${self.price_cache[symbol]:.4f}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحديث {symbol}: {e}")
            return False
    
    def update_all_cryptos(self, symbols, max_workers=10):
        """تحديث جميع العملات"""
        print(f"🔄 بدء تحديث {len(symbols)} عملة...")
        
        updated_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_symbol = {executor.submit(self.update_crypto_data, symbol): symbol for symbol in symbols}
            
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    if future.result():
                        updated_count += 1
                except Exception as e:
                    print(f"❌ خطأ في {symbol}: {e}")
        
        print(f"✅ تم تحديث {updated_count}/{len(symbols)} عملة")
        return updated_count
    
    def get_crypto_data(self, symbol):
        """الحصول على بيانات العملة (محدثة أو من الكاش)"""
        file_path = os.path.join(self.data_dir, f"{symbol}_live.csv")
        
        # التحقق من وجود البيانات وحداثتها
        if os.path.exists(file_path):
            last_modified = datetime.fromtimestamp(os.path.getmtime(file_path))
            if datetime.now() - last_modified < timedelta(hours=2):
                # البيانات حديثة، تحميلها
                try:
                    df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                    return df
                except:
                    pass
        
        # البيانات غير موجودة أو قديمة، تحديثها
        if self.update_crypto_data(symbol):
            try:
                df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                return df
            except:
                pass
        
        return None
    
    def get_live_price(self, symbol):
        """الحصول على السعر الحي"""
        # التحقق من الكاش
        if symbol in self.price_cache:
            last_update = self.last_update.get(symbol, datetime.min)
            if datetime.now() - last_update < timedelta(minutes=5):
                return self.price_cache[symbol]
        
        # جلب سعر جديد
        price = self.get_best_live_price(symbol)
        if price:
            self.price_cache[symbol] = price
            self.last_update[symbol] = datetime.now()
            return price
        
        # إذا فشل، إرجاع من الكاش إن وجد
        return self.price_cache.get(symbol, None)
    
    def get_all_available_symbols(self):
        """الحصول على جميع العملات المتاحة من Binance"""
        try:
            url = "https://api.binance.com/api/v3/exchangeInfo"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                symbols = []
                
                for symbol_info in data['symbols']:
                    if (symbol_info['symbol'].endswith('USDT') and 
                        symbol_info['status'] == 'TRADING'):
                        symbol = symbol_info['symbol'].replace('USDT', '')
                        if symbol not in ['USDC', 'BUSD', 'DAI', 'TUSD', 'USDD', 'FDUSD']:
                            symbols.append(symbol)
                
                return symbols[:200]  # أفضل 200 عملة
            
        except Exception as e:
            print(f"❌ خطأ في جلب قائمة العملات: {e}")
        
        # قائمة احتياطية
        return [
            'BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'DOGE', 'SOL', 'TRX', 'DOT', 'MATIC',
            'SHIB', 'AVAX', 'UNI', 'LINK', 'ATOM', 'XMR', 'ETC', 'BCH', 'LTC', 'FIL',
            'APT', 'NEAR', 'VET', 'ICP', 'GRT', 'SAND', 'MANA', 'ALGO', 'FLOW', 'XTZ'
        ]
    
    def start_auto_update(self, symbols, update_interval_hours=1):
        """بدء التحديث التلقائي"""
        def update_loop():
            while True:
                try:
                    print(f"🔄 بدء دورة تحديث تلقائي - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    self.update_all_cryptos(symbols)
                    print(f"⏰ انتظار {update_interval_hours} ساعة للدورة التالية...")
                    time.sleep(update_interval_hours * 3600)
                except Exception as e:
                    print(f"❌ خطأ في التحديث التلقائي: {e}")
                    time.sleep(300)  # انتظار 5 دقائق عند الخطأ
        
        # تشغيل في خيط منفصل
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
        print(f"🚀 تم بدء التحديث التلقائي كل {update_interval_hours} ساعة")

def main():
    """اختبار النظام"""
    live_data = LiveDataManager()
    
    # جلب قائمة العملات
    symbols = live_data.get_all_available_symbols()
    print(f"📊 تم العثور على {len(symbols)} عملة")
    
    # تحديث أول 10 عملات للاختبار
    test_symbols = symbols[:10]
    live_data.update_all_cryptos(test_symbols)
    
    # اختبار جلب البيانات
    for symbol in test_symbols[:3]:
        data = live_data.get_crypto_data(symbol)
        if data is not None:
            print(f"✅ {symbol}: {len(data)} صف، آخر سعر: ${data['close'].iloc[-1]:.4f}")
        else:
            print(f"❌ فشل جلب بيانات {symbol}")

if __name__ == "__main__":
    main()
