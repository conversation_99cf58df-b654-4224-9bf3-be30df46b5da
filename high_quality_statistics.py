#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إحصائيات عالي الجودة - إشارات قليلة وقوية
الاستراتيجية عالية الجودة:
- احتمالية نجاح فوق 60% (عالية الجودة)
- نسبة صعود أعلى من 6% (مجدية)
- إشارات مركبة (تطابق عدة مؤشرات)

المؤشرات المركبة عالية الجودة:
1. RSI + MACD معاً (قوة مضاعفة)
2. OBV + ADX معاً (حجم + اتجاه)
3. الثلاثة الأساسية معاً (RSI + MACD + OBV)
4. الخمسة معاً (إشارة ذهبية)
"""

import os
import pandas as pd
import json
import numpy as np

# إعدادات
DATA_DIR = "data"
OUTPUT_FILE = "high_quality_weights.json"

# المؤشرات المركبة عالية الجودة
INDICATORS = [
    'rsi_macd_combo',       # RSI + MACD معاً (قوة مضاعفة)
    'obv_adx_combo',        # OBV + ADX معاً (حجم + اتجاه)
    'triple_combo',         # RSI + MACD + OBV معاً
    'quad_combo',           # RSI + MACD + OBV + ADX معاً
    'golden_combo'          # الخمسة معاً (إشارة ذهبية)
]

def detect_high_quality_signals(df):
    """كشف الإشارات عالية الجودة المركبة"""
    signals = dict.fromkeys(INDICATORS, False)

    # التأكد من وجود بيانات كافية
    if len(df) < 20:
        return signals

    try:
        # === فحص المؤشرات الفردية أولاً ===
        individual_signals = {}
        
        # RSI قوي
        if 'rsi' in df.columns and len(df) > 2:
            current_rsi = df['rsi'].iloc[-1]
            prev_rsi = df['rsi'].iloc[-2]
            prev2_rsi = df['rsi'].iloc[-3]
            individual_signals['rsi'] = (
                35 <= current_rsi <= 65 and     # منطقة صحية
                current_rsi > prev_rsi > prev2_rsi  # تحسن مستمر
            )

        # MACD قوي
        if 'macd' in df.columns and 'macd_signal' in df.columns and len(df) > 2:
            current_macd = df['macd'].iloc[-1]
            current_signal = df['macd_signal'].iloc[-1]
            prev_macd = df['macd'].iloc[-2]
            prev_signal = df['macd_signal'].iloc[-2]
            
            # تقاطع إيجابي قوي أو تحسن مستمر فوق الصفر
            bullish_cross = (current_macd > current_signal and prev_macd <= prev_signal)
            strong_uptrend = (current_macd > 0 and current_macd > prev_macd and current_macd > current_signal)
            
            individual_signals['macd'] = bullish_cross or strong_uptrend

        # OBV قوي
        if 'obv' in df.columns and len(df) >= 5:
            obv_values = df['obv'].iloc[-5:].values
            # اتجاه صاعد مستمر في آخر 5 فترات
            individual_signals['obv'] = all(obv_values[i] < obv_values[i+1] for i in range(3))

        # ADX قوي (إذا متوفر)
        if all(col in df.columns for col in ['adx', 'adx_pos', 'adx_neg']):
            current_adx = df['adx'].iloc[-1]
            current_pos = df['adx_pos'].iloc[-1]
            current_neg = df['adx_neg'].iloc[-1]
            prev_adx = df['adx'].iloc[-2] if len(df) > 1 else current_adx
            
            individual_signals['adx'] = (
                current_adx > 25 and           # قوة اتجاه عالية
                current_pos > current_neg * 1.5 and  # هيمنة صاعدة
                current_adx > prev_adx         # قوة متزايدة
            )

        # CMF قوي (إذا متوفر)
        if 'cmf' in df.columns and len(df) >= 3:
            cmf_values = df['cmf'].iloc[-3:].values
            # تدفق نقدي إيجابي مستمر
            individual_signals['cmf'] = all(val > 0.1 for val in cmf_values)

        # === الآن تكوين الإشارات المركبة ===
        
        # RSI + MACD معاً (قوة مضاعفة)
        signals['rsi_macd_combo'] = (
            individual_signals.get('rsi', False) and 
            individual_signals.get('macd', False)
        )

        # OBV + ADX معاً (حجم + اتجاه)
        signals['obv_adx_combo'] = (
            individual_signals.get('obv', False) and 
            individual_signals.get('adx', False)
        )

        # الثلاثة الأساسية معاً
        signals['triple_combo'] = (
            individual_signals.get('rsi', False) and 
            individual_signals.get('macd', False) and 
            individual_signals.get('obv', False)
        )

        # الأربعة معاً
        signals['quad_combo'] = (
            individual_signals.get('rsi', False) and 
            individual_signals.get('macd', False) and 
            individual_signals.get('obv', False) and 
            individual_signals.get('adx', False)
        )

        # الخمسة معاً (إشارة ذهبية)
        signals['golden_combo'] = (
            individual_signals.get('rsi', False) and 
            individual_signals.get('macd', False) and 
            individual_signals.get('obv', False) and 
            individual_signals.get('adx', False) and 
            individual_signals.get('cmf', False)
        )

    except Exception as e:
        print(f"⚠️ خطأ في كشف الإشارات عالية الجودة: {e}")

    return signals

def analyze_file_high_quality(file_path):
    """تحليل ملف واحد بالاستراتيجية عالية الجودة"""
    try:
        df = pd.read_csv(file_path)
        
        # التأكد من وجود الأعمدة الأساسية
        required_columns = ['close', 'rsi', 'macd', 'macd_signal', 'obv']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return {}

        results = {}
        
        for i in range(50, len(df) - 12):  # ترك مساحة للتحليل
            signals = detect_high_quality_signals(df.iloc[:i+1])
            
            # البحث عن صعود قوي في الـ 12 فترة القادمة
            current_price = df['close'].iloc[i]
            future_prices = df['close'].iloc[i+1:i+13]
            
            if len(future_prices) == 0:
                continue
                
            max_future_price = future_prices.max()
            max_price_change = (max_future_price - current_price) / current_price
            
            # معايير نجاح عالية الجودة
            for periods in range(1, min(13, len(future_prices) + 1)):
                future_price = df['close'].iloc[i + periods]
                period_change = (future_price - current_price) / current_price
                
                # عتبات عالية للصعود القوي
                if periods <= 3:  # خلال 12 ساعة
                    success_threshold = 0.06  # 6%
                elif periods <= 6:  # خلال 24 ساعة
                    success_threshold = 0.08  # 8%
                else:  # خلال 48+ ساعة
                    success_threshold = 0.10  # 10%
                
                if period_change >= success_threshold:
                    for indicator, signal in signals.items():
                        if signal:
                            if indicator not in results:
                                results[indicator] = {
                                    'total_signals': 0,
                                    'successful_signals': 0,
                                    'returns': [],
                                    'time_to_targets': []
                                }
                            
                            results[indicator]['total_signals'] += 1
                            results[indicator]['successful_signals'] += 1
                            results[indicator]['returns'].append(period_change * 100)
                            results[indicator]['time_to_targets'].append(periods)
                    break
            else:
                # لم يحقق النجاح
                for indicator, signal in signals.items():
                    if signal:
                        if indicator not in results:
                            results[indicator] = {
                                'total_signals': 0,
                                'successful_signals': 0,
                                'returns': [],
                                'time_to_targets': []
                            }
                        results[indicator]['total_signals'] += 1
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في تحليل {file_path}: {e}")
        return {}

def main():
    print("💎 بدء التحليل عالي الجودة - إشارات قليلة وقوية...")
    print("🎯 عتبات عالية: احتمالية >60%, صعود >6%")
    print("🔗 إشارات مركبة: تطابق عدة مؤشرات معاً")
    print(f"📁 البحث عن ملفات CSV في مجلد: {DATA_DIR}")
    
    # التحقق من وجود مجلد البيانات
    if not os.path.exists(DATA_DIR):
        print(f"❌ مجلد البيانات غير موجود: {DATA_DIR}")
        return
    
    # عد ملفات CSV
    csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]
    print(f"📁 وجد {len(csv_files)} ملف CSV في المجلد")
    
    # إحصائيات شاملة
    all_stats = {indicator: {
        'total_signals': 0,
        'successful_signals': 0,
        'returns': [],
        'time_to_targets': []
    } for indicator in INDICATORS}
    
    files_processed = 0
    
    for filename in csv_files:
        file_path = os.path.join(DATA_DIR, filename)
        
        file_results = analyze_file_high_quality(file_path)
        
        # دمج النتائج
        for indicator, stats in file_results.items():
            if indicator in all_stats:
                all_stats[indicator]['total_signals'] += stats['total_signals']
                all_stats[indicator]['successful_signals'] += stats['successful_signals']
                all_stats[indicator]['returns'].extend(stats['returns'])
                all_stats[indicator]['time_to_targets'].extend(stats['time_to_targets'])
        
        files_processed += 1
        
        if files_processed % 50 == 0:
            print(f"📈 تم تحليل {files_processed}/{len(csv_files)} ملف...")
    
    # حساب الإحصائيات النهائية - عتبات عالية الجودة
    final_weights = {}
    
    for indicator, stats in all_stats.items():
        if stats['total_signals'] > 5:  # حد أدنى للإشارات النادرة
            success_rate = stats['successful_signals'] / stats['total_signals']
            avg_return = np.mean(stats['returns']) if stats['returns'] else 0
            avg_time = np.mean(stats['time_to_targets']) if stats['time_to_targets'] else 0
            
            # تطبيق العتبات العالية
            if success_rate >= 0.60 and avg_return >= 6.0:  # 60% نجاح و 6% عائد
                # حساب نسبة شارب (العائد / المخاطر)
                returns_std = np.std(stats['returns']) if len(stats['returns']) > 1 else 1
                sharpe_ratio = avg_return / returns_std if returns_std > 0 else 0
                
                # حساب الوزن: احتمالية × عائد × استقرار
                weight = success_rate * avg_return * max(0.1, min(2.0, sharpe_ratio / 10))
                
                final_weights[indicator] = {
                    'weight': weight,
                    'success_probability': success_rate,
                    'average_return': avg_return,
                    'average_time_to_target': avg_time,
                    'sharpe_ratio': sharpe_ratio,
                    'total_signals': stats['total_signals'],
                    'successful_signals': stats['successful_signals']
                }
    
    # تطبيع الأوزان
    total_weight = sum(v['weight'] for v in final_weights.values())
    if total_weight > 0:
        for indicator in final_weights:
            final_weights[indicator]['weight'] = final_weights[indicator]['weight'] / total_weight
    
    # حفظ النتائج
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(final_weights, f, indent=4, ensure_ascii=False)
    
    # عرض النتائج
    print(f"\n✅ تم تحليل {files_processed} ملف")
    print(f"💎 إحصائيات المؤشرات عالية الجودة:")
    print("-" * 80)
    
    if final_weights:
        # ترتيب المؤشرات حسب الوزن
        sorted_indicators = sorted(final_weights.items(), key=lambda x: x[1]['weight'], reverse=True)
        
        for indicator, stats in sorted_indicators:
            print(f"\n🔹 {indicator}:")
            print(f"   الوزن: {stats['weight']:.4f}")
            print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
            print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
            print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
            print(f"   نسبة شارب: {stats['sharpe_ratio']:.3f}")
            print(f"   إجمالي الإشارات: {stats['total_signals']:,}")
            print(f"   الإشارات الناجحة: {stats['successful_signals']:,}")
        
        print(f"\n📊 ملخص:")
        print(f"   المؤشرات المقبولة: {len(final_weights)}/5")
        print(f"   إجمالي الأوزان: {sum(v['weight'] for v in final_weights.values()):.3f}")
        
        # حساب متوسط الأداء العام
        avg_success = np.mean([v['success_probability'] for v in final_weights.values()])
        avg_return = np.mean([v['average_return'] for v in final_weights.values()])
        total_signals = sum([v['total_signals'] for v in final_weights.values()])
        
        print(f"\n💎 الأداء عالي الجودة:")
        print(f"   متوسط احتمالية النجاح: {avg_success:.1%}")
        print(f"   متوسط العائد المتوقع: {avg_return:+.2f}%")
        print(f"   إجمالي الإشارات عالية الجودة: {total_signals:,}")
        
    else:
        print("\n⚠️ لا توجد مؤشرات تحقق العتبات عالية الجودة!")
        print("   العتبات: احتمالية ≥60%, عائد ≥6%")
        print("   💡 هذا طبيعي - الإشارات عالية الجودة نادرة!")
    
    print(f"\n📁 تم حفظ الأوزان عالية الجودة في {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
