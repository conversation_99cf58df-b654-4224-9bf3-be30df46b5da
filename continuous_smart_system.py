#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النظام المستمر الذكي للعملات المشفرة
- فحص كل ساعة
- تقييم الأداء المستمر
- تحسين ذاتي بناءً على النتائج
"""

import time
import threading
import json
import os
from datetime import datetime, timedelta
from integrated_crypto_system import IntegratedCryptoSystem

class ContinuousSmartSystem:
    """النظام المستمر الذكي"""
    
    def __init__(self):
        self.crypto_system = IntegratedCryptoSystem()
        self.running = False
        
        # إعدادات التشغيل
        self.analysis_interval = 3600  # ساعة واحدة
        self.performance_check_interval = 1800  # 30 دقيقة
        
        # إعدادات التحسين الذاتي
        self.performance_history_file = "performance_history.json"
        self.adaptive_settings_file = "adaptive_settings.json"
        
        # إعدادات قابلة للتكيف
        self.adaptive_settings = {
            "min_confidence_adjustment": 0.0,  # تعديل عتبة الثقة
            "target_multiplier": 1.0,          # مضاعف الأهداف
            "success_rate_threshold": 0.70,    # عتبة النجاح المطلوبة
            "last_performance_check": None
        }
        
        self.load_adaptive_settings()
        
        print("🚀 النظام المستمر الذكي جاهز")
        print("⏰ فحص كل ساعة + تقييم كل 30 دقيقة")
        print("🧠 تحسين ذاتي بناءً على الأداء")
    
    def load_adaptive_settings(self):
        """تحميل الإعدادات التكيفية"""
        try:
            if os.path.exists(self.adaptive_settings_file):
                with open(self.adaptive_settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.adaptive_settings.update(saved_settings)
                print(f"📊 تم تحميل الإعدادات التكيفية: {self.adaptive_settings}")
        except Exception as e:
            print(f"⚠️ خطأ في تحميل الإعدادات التكيفية: {e}")
    
    def save_adaptive_settings(self):
        """حفظ الإعدادات التكيفية"""
        try:
            with open(self.adaptive_settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.adaptive_settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطأ في حفظ الإعدادات التكيفية: {e}")
    
    def load_performance_history(self):
        """تحميل تاريخ الأداء"""
        try:
            if os.path.exists(self.performance_history_file):
                with open(self.performance_history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            return []
    
    def save_performance_history(self, history):
        """حفظ تاريخ الأداء"""
        try:
            with open(self.performance_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطأ في حفظ تاريخ الأداء: {e}")
    
    def evaluate_current_performance(self):
        """تقييم الأداء الحالي"""
        try:
            print("📊 تقييم الأداء الحالي...")
            
            # فحص التوصيات المنتهية الصلاحية
            stats = self.crypto_system.check_expired_recommendations()
            
            current_time = datetime.now()
            performance_record = {
                'timestamp': current_time.isoformat(),
                'success_rate': stats.get('success_rate', 0),
                'total_recommendations': stats.get('total_recommendations', 0),
                'successful_recommendations': stats.get('successful_recommendations', 0),
                'adaptive_settings': self.adaptive_settings.copy()
            }
            
            # حفظ في تاريخ الأداء
            history = self.load_performance_history()
            history.append(performance_record)
            
            # الاحتفاظ بآخر 100 سجل فقط
            if len(history) > 100:
                history = history[-100:]
            
            self.save_performance_history(history)
            
            print(f"📈 معدل النجاح الحالي: {stats.get('success_rate', 0):.1%}")
            print(f"📊 إجمالي التوصيات: {stats.get('total_recommendations', 0)}")
            
            return performance_record
            
        except Exception as e:
            print(f"❌ خطأ في تقييم الأداء: {e}")
            return None
    
    def analyze_performance_trends(self):
        """تحليل اتجاهات الأداء"""
        try:
            history = self.load_performance_history()
            
            if len(history) < 5:
                return None
            
            # آخر 10 سجلات
            recent_records = history[-10:]
            
            # حساب متوسط معدل النجاح
            recent_success_rates = [r['success_rate'] for r in recent_records if r['success_rate'] > 0]
            
            if not recent_success_rates:
                return None
            
            avg_success_rate = sum(recent_success_rates) / len(recent_success_rates)
            
            # تحليل الاتجاه
            if len(recent_success_rates) >= 3:
                trend = "improving" if recent_success_rates[-1] > recent_success_rates[-3] else "declining"
            else:
                trend = "stable"
            
            analysis = {
                'avg_success_rate': avg_success_rate,
                'trend': trend,
                'sample_size': len(recent_success_rates),
                'latest_rate': recent_success_rates[-1] if recent_success_rates else 0
            }
            
            print(f"📈 تحليل الاتجاهات: معدل النجاح {avg_success_rate:.1%} | الاتجاه: {trend}")
            
            return analysis
            
        except Exception as e:
            print(f"❌ خطأ في تحليل الاتجاهات: {e}")
            return None
    
    def adapt_system_settings(self, performance_analysis):
        """تكييف إعدادات النظام بناءً على الأداء"""
        try:
            if not performance_analysis:
                return
            
            avg_success_rate = performance_analysis['avg_success_rate']
            trend = performance_analysis['trend']
            
            print(f"🧠 تكييف إعدادات النظام...")
            
            # تعديل عتبة الثقة
            if avg_success_rate < self.adaptive_settings['success_rate_threshold']:
                # أداء ضعيف - زيادة عتبة الثقة
                if trend == "declining":
                    self.adaptive_settings['min_confidence_adjustment'] += 0.05
                    print(f"📈 زيادة عتبة الثقة بـ 5% (أداء ضعيف)")
            else:
                # أداء جيد - تقليل عتبة الثقة تدريجياً
                if trend == "improving" and self.adaptive_settings['min_confidence_adjustment'] > 0:
                    self.adaptive_settings['min_confidence_adjustment'] -= 0.02
                    print(f"📉 تقليل عتبة الثقة بـ 2% (أداء جيد)")
            
            # تعديل مضاعف الأهداف
            if avg_success_rate > 0.85:
                # أداء ممتاز - زيادة الأهداف
                self.adaptive_settings['target_multiplier'] = min(1.2, self.adaptive_settings['target_multiplier'] + 0.05)
                print(f"🎯 زيادة مضاعف الأهداف إلى {self.adaptive_settings['target_multiplier']:.2f}")
            elif avg_success_rate < 0.60:
                # أداء ضعيف - تقليل الأهداف
                self.adaptive_settings['target_multiplier'] = max(0.8, self.adaptive_settings['target_multiplier'] - 0.05)
                print(f"🎯 تقليل مضاعف الأهداف إلى {self.adaptive_settings['target_multiplier']:.2f}")
            
            # تحديث وقت آخر فحص
            self.adaptive_settings['last_performance_check'] = datetime.now().isoformat()
            
            # حفظ الإعدادات
            self.save_adaptive_settings()
            
            print(f"✅ تم تكييف الإعدادات: ثقة +{self.adaptive_settings['min_confidence_adjustment']:.2f}, أهداف ×{self.adaptive_settings['target_multiplier']:.2f}")
            
        except Exception as e:
            print(f"❌ خطأ في تكييف الإعدادات: {e}")
    
    def run_analysis_cycle(self):
        """تشغيل دورة تحليل واحدة"""
        try:
            print(f"🔍 بدء دورة التحليل - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # تحميل النموذج إذا لم يكن محملاً
            if not self.crypto_system.model_loaded:
                if not self.crypto_system.load_model():
                    print("❌ فشل تحميل النموذج")
                    return False
            
            # تطبيق الإعدادات التكيفية
            original_min_confidence = self.crypto_system.min_confidence
            original_target_return = self.crypto_system.target_return
            
            # تعديل الإعدادات مؤقتاً
            self.crypto_system.min_confidence += self.adaptive_settings['min_confidence_adjustment']
            
            print(f"🎯 إعدادات مكيفة: ثقة {self.crypto_system.min_confidence:.2f}, مضاعف أهداف {self.adaptive_settings['target_multiplier']:.2f}")
            
            # تشغيل التحليل
            start_time = time.time()
            recommendations_count = self.crypto_system.analyze_and_send()
            end_time = time.time()

            # فحص إشارات الخروج
            exit_signals = self.crypto_system.exit_signals_system.check_exit_signals()

            # إرسال إشارات الخروج إذا وجدت
            if exit_signals:
                for exit_signal in exit_signals:
                    exit_message = self.crypto_system.exit_signals_system.format_exit_signal_message(exit_signal)
                    if self.crypto_system.send_telegram_message(exit_message):
                        print(f"🚨 تم إرسال إشارة خروج لـ {exit_signal['symbol']}: {exit_signal['signal_type']}")

                exit_summary = self.crypto_system.exit_signals_system.get_exit_signals_summary(exit_signals)
                print(f"🚨 إشارات الخروج: {exit_summary}")

            # استعادة الإعدادات الأصلية
            self.crypto_system.min_confidence = original_min_confidence

            analysis_time = end_time - start_time
            print(f"✅ انتهت دورة التحليل في {analysis_time:.1f} ثانية")
            print(f"📊 توصيات مرسلة: {len(recommendations_count) if isinstance(recommendations_count, list) else 'غير محدد'}")

            return True
            
        except Exception as e:
            print(f"❌ خطأ في دورة التحليل: {e}")
            return False
    
    def performance_check_loop(self):
        """حلقة فحص الأداء"""
        while self.running:
            try:
                # تقييم الأداء
                performance = self.evaluate_current_performance()
                
                if performance:
                    # تحليل الاتجاهات
                    trends = self.analyze_performance_trends()
                    
                    # تكييف الإعدادات
                    self.adapt_system_settings(trends)
                
                # انتظار حتى الفحص التالي
                time.sleep(self.performance_check_interval)
                
            except Exception as e:
                print(f"❌ خطأ في حلقة فحص الأداء: {e}")
                time.sleep(300)  # انتظار 5 دقائق عند الخطأ
    
    def analysis_loop(self):
        """حلقة التحليل المستمر"""
        while self.running:
            try:
                # تشغيل دورة التحليل
                self.run_analysis_cycle()
                
                # انتظار حتى الدورة التالية
                print(f"⏰ انتظار {self.analysis_interval/3600:.1f} ساعة للدورة التالية...")
                time.sleep(self.analysis_interval)
                
            except Exception as e:
                print(f"❌ خطأ في حلقة التحليل: {e}")
                time.sleep(300)  # انتظار 5 دقائق عند الخطأ
    
    def start(self):
        """بدء النظام المستمر"""
        if self.running:
            print("⚠️ النظام يعمل بالفعل")
            return
        
        self.running = True
        
        print("🚀 بدء النظام المستمر الذكي...")
        
        # تحليل أولي
        print("📊 تحليل أولي...")
        self.run_analysis_cycle()
        
        # بدء خيوط التشغيل
        analysis_thread = threading.Thread(target=self.analysis_loop, daemon=True)
        performance_thread = threading.Thread(target=self.performance_check_loop, daemon=True)
        
        analysis_thread.start()
        performance_thread.start()
        
        print("✅ تم بدء النظام المستمر الذكي")
        print("🔍 خيط التحليل: كل ساعة")
        print("📊 خيط تقييم الأداء: كل 30 دقيقة")
        print("🧠 التحسين الذاتي: مفعل")
        
        return analysis_thread, performance_thread
    
    def stop(self):
        """إيقاف النظام المستمر"""
        self.running = False
        print("⏹️ تم إيقاف النظام المستمر الذكي")

def print_banner():
    """عرض شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║              🧠 النظام المستمر الذكي للعملات المشفرة              ║
║                                                              ║
║  🔍 فحص كل ساعة + تقييم كل 30 دقيقة                           ║
║  📊 تحسين ذاتي بناءً على الأداء                               ║
║  🎯 تكييف الإعدادات تلقائياً                                   ║
║  📱 أفضل 5 توصيات مجمعة                                       ║
║  💰 بيانات حية من مصادر متعددة                                ║
║                                                              ║
║  المطور: نظام الذكاء الاصطناعي المتقدم                            ║
║  الإصدار: النظام المستمر الذكي 2025                             ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    smart_system = ContinuousSmartSystem()
    
    try:
        print("\n🚀 بدء النظام المستمر الذكي...")
        print("⏹️ اضغط Ctrl+C لإيقاف النظام\n")
        
        # بدء النظام
        threads = smart_system.start()
        
        # انتظار إشارة الإيقاف
        while smart_system.running:
            time.sleep(1)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم طلب إيقاف النظام...")
        smart_system.stop()
        print("✅ تم إيقاف النظام بأمان")
    
    except Exception as e:
        print(f"\n❌ خطأ في النظام: {e}")
        smart_system.stop()

if __name__ == "__main__":
    main()
