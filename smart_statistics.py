#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إحصائيات ذكي - 5 مؤشرات عالية الاعتماد
الاستراتيجية الذكية مع عتبات صارمة:
- احتمالية نجاح فوق 60%
- نسبة صعود أعلى من 10%

المؤشرات:
1. RSI - كشف البيع/الشراء المفرط (⭐⭐⭐⭐)
2. MACD - إشارات الموجات المتوسطة (⭐⭐⭐⭐)  
3. OBV - تدفق السيولة الحقيقية (⭐⭐⭐⭐)
4. ADX - قوة الاتجاه (⭐⭐⭐⭐)
5. CMF - فلترة الصعود الكاذب (⭐⭐⭐)
"""

import os
import pandas as pd
import json
import numpy as np

# إعدادات
DATA_DIR = "data"
OUTPUT_FILE = "smart_weights.json"

# أفضل 5 مؤشرات عالية الاعتماد
INDICATORS = [
    'rsi_smart',        # RSI ذكي مع الدعوم والمقاومات
    'macd_smart',       # MACD مع تقاطعات قوية
    'obv_smart',        # OBV تدفق السيولة الحقيقية
    'adx_smart',        # ADX قوة الاتجاه
    'cmf_smart'         # CMF فلترة الصعود الكاذب
]

def detect_smart_signals(df):
    """كشف الإشارات الذكية للمؤشرات الخمسة"""
    signals = dict.fromkeys(INDICATORS, False)

    # التأكد من وجود بيانات كافية
    if len(df) < 20:
        return signals

    try:
        # === RSI الذكي: مع الدعوم والمقاومات ===
        if 'rsi' in df.columns:
            current_rsi = df['rsi'].iloc[-1]
            prev_rsi = df['rsi'].iloc[-2] if len(df) > 1 else current_rsi
            
            # RSI يخرج من منطقة البيع المفرط ويتحسن
            signals['rsi_smart'] = (
                30 <= current_rsi <= 65 and  # منطقة صحية
                current_rsi > prev_rsi and   # تحسن
                current_rsi > 35             # خروج من البيع المفرط
            )

        # === MACD الذكي: تقاطعات قوية ===
        if 'macd' in df.columns and 'macd_signal' in df.columns:
            current_macd = df['macd'].iloc[-1]
            current_signal = df['macd_signal'].iloc[-1]
            prev_macd = df['macd'].iloc[-2] if len(df) > 1 else current_macd
            prev_signal = df['macd_signal'].iloc[-2] if len(df) > 1 else current_signal
            
            # تقاطع إيجابي قوي أو فوق خط الصفر مع تحسن
            bullish_cross = (current_macd > current_signal and prev_macd <= prev_signal)
            above_zero_improving = (current_macd > 0 and current_macd > prev_macd)
            
            signals['macd_smart'] = bullish_cross or above_zero_improving

        # === OBV الذكي: تدفق السيولة الحقيقية ===
        if 'obv' in df.columns:
            if len(df) >= 5:
                current_obv = df['obv'].iloc[-1]
                obv_sma = df['obv'].rolling(window=5).mean().iloc[-1]
                prev_obv_sma = df['obv'].rolling(window=5).mean().iloc[-2] if len(df) > 5 else obv_sma
                
                # OBV فوق المتوسط ومتزايد (تدفق سيولة حقيقي)
                signals['obv_smart'] = (
                    current_obv > obv_sma and
                    obv_sma > prev_obv_sma
                )

        # === ADX الذكي: قوة الاتجاه ===
        if 'adx' in df.columns and 'adx_pos' in df.columns and 'adx_neg' in df.columns:
            current_adx = df['adx'].iloc[-1]
            current_pos = df['adx_pos'].iloc[-1]
            current_neg = df['adx_neg'].iloc[-1]
            prev_adx = df['adx'].iloc[-2] if len(df) > 1 else current_adx
            
            # اتجاه صاعد قوي ومتزايد
            signals['adx_smart'] = (
                current_adx > 25 and           # قوة اتجاه جيدة
                current_pos > current_neg and  # اتجاه صاعد
                current_adx > prev_adx         # قوة متزايدة
            )

        # === CMF الذكي: فلترة الصعود الكاذب ===
        if 'cmf' in df.columns:
            current_cmf = df['cmf'].iloc[-1]
            cmf_avg = df['cmf'].rolling(window=10).mean().iloc[-1] if len(df) >= 10 else current_cmf
            
            # تدفق نقدي إيجابي قوي
            signals['cmf_smart'] = (
                current_cmf > 0.1 and      # تدفق إيجابي قوي
                current_cmf > cmf_avg      # أعلى من المتوسط
            )

    except Exception as e:
        print(f"⚠️ خطأ في كشف الإشارات الذكية: {e}")

    return signals

def analyze_file_smart(file_path):
    """تحليل ملف واحد بالاستراتيجية الذكية"""
    try:
        df = pd.read_csv(file_path)
        
        # التأكد من وجود الأعمدة المطلوبة
        required_columns = ['close', 'rsi', 'macd', 'macd_signal', 'obv', 'adx', 'adx_pos', 'adx_neg', 'cmf']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"⚠️ أعمدة مفقودة في {file_path}: {missing_columns}")
            return {}

        results = {}
        
        for i in range(50, len(df) - 12):  # ترك مساحة للتحليل
            signals = detect_smart_signals(df.iloc[:i+1])
            
            # البحث عن صعود قوي في الـ 12 فترة القادمة
            current_price = df['close'].iloc[i]
            future_prices = df['close'].iloc[i+1:i+13]
            
            if len(future_prices) == 0:
                continue
                
            max_future_price = future_prices.max()
            max_price_change = (max_future_price - current_price) / current_price
            
            # معايير النجاح صارمة للحصول على إشارات عالية الجودة
            for periods in range(1, min(13, len(future_prices) + 1)):
                future_price = df['close'].iloc[i + periods]
                period_change = (future_price - current_price) / current_price
                
                # عتبات صارمة للصعود القوي
                if periods <= 3:  # خلال 12 ساعة
                    success_threshold = 0.08  # 8%
                elif periods <= 6:  # خلال 24 ساعة
                    success_threshold = 0.12  # 12%
                else:  # خلال 48+ ساعة
                    success_threshold = 0.15  # 15%
                
                if period_change >= success_threshold:
                    for indicator, signal in signals.items():
                        if signal:
                            if indicator not in results:
                                results[indicator] = {
                                    'total_signals': 0,
                                    'successful_signals': 0,
                                    'returns': [],
                                    'time_to_targets': []
                                }
                            
                            results[indicator]['total_signals'] += 1
                            results[indicator]['successful_signals'] += 1
                            results[indicator]['returns'].append(period_change * 100)
                            results[indicator]['time_to_targets'].append(periods)
                    break
            else:
                # لم يحقق النجاح
                for indicator, signal in signals.items():
                    if signal:
                        if indicator not in results:
                            results[indicator] = {
                                'total_signals': 0,
                                'successful_signals': 0,
                                'returns': [],
                                'time_to_targets': []
                            }
                        results[indicator]['total_signals'] += 1
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في تحليل {file_path}: {e}")
        return {}

def main():
    print("🧠 بدء التحليل الذكي - 5 مؤشرات عالية الاعتماد...")
    print("🎯 عتبات صارمة: احتمالية >60%, صعود >10%")
    print(f"📊 البحث عن ملفات CSV في مجلد: {DATA_DIR}")
    
    # التحقق من وجود مجلد البيانات
    if not os.path.exists(DATA_DIR):
        print(f"❌ مجلد البيانات غير موجود: {DATA_DIR}")
        return
    
    # عد ملفات CSV
    csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]
    print(f"📁 وجد {len(csv_files)} ملف CSV في المجلد")
    
    # إحصائيات شاملة
    all_stats = {indicator: {
        'total_signals': 0,
        'successful_signals': 0,
        'returns': [],
        'time_to_targets': []
    } for indicator in INDICATORS}
    
    files_processed = 0
    
    for filename in csv_files:
        file_path = os.path.join(DATA_DIR, filename)
        print(f"🔍 تحليل: {filename}")
        
        file_results = analyze_file_smart(file_path)
        
        # دمج النتائج
        for indicator, stats in file_results.items():
            if indicator in all_stats:
                all_stats[indicator]['total_signals'] += stats['total_signals']
                all_stats[indicator]['successful_signals'] += stats['successful_signals']
                all_stats[indicator]['returns'].extend(stats['returns'])
                all_stats[indicator]['time_to_targets'].extend(stats['time_to_targets'])
        
        files_processed += 1
        
        if files_processed % 50 == 0:
            print(f"📈 تم تحليل {files_processed}/{len(csv_files)} ملف...")
    
    # حساب الإحصائيات النهائية مع العتبات الصارمة
    final_weights = {}
    
    for indicator, stats in all_stats.items():
        if stats['total_signals'] > 100:  # حد أدنى للإشارات
            success_rate = stats['successful_signals'] / stats['total_signals']
            avg_return = np.mean(stats['returns']) if stats['returns'] else 0
            avg_time = np.mean(stats['time_to_targets']) if stats['time_to_targets'] else 0
            
            # تطبيق العتبات الصارمة
            if success_rate >= 0.60 and avg_return >= 10.0:  # 60% نجاح و 10% عائد
                # حساب نسبة شارب (العائد / المخاطر)
                returns_std = np.std(stats['returns']) if len(stats['returns']) > 1 else 1
                sharpe_ratio = avg_return / returns_std if returns_std > 0 else 0
                
                # حساب الوزن: احتمالية × عائد × جودة
                weight = success_rate * avg_return * max(0, sharpe_ratio / 100)
                
                final_weights[indicator] = {
                    'weight': weight,
                    'success_probability': success_rate,
                    'average_return': avg_return,
                    'average_time_to_target': avg_time,
                    'sharpe_ratio': sharpe_ratio,
                    'total_signals': stats['total_signals'],
                    'successful_signals': stats['successful_signals']
                }
    
    # تطبيع الأوزان
    total_weight = sum(v['weight'] for v in final_weights.values())
    if total_weight > 0:
        for indicator in final_weights:
            final_weights[indicator]['weight'] = final_weights[indicator]['weight'] / total_weight
    
    # حفظ النتائج
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(final_weights, f, indent=4, ensure_ascii=False)
    
    # عرض النتائج
    print(f"\n✅ تم تحليل {files_processed} ملف")
    print(f"🧠 إحصائيات المؤشرات الذكية (عتبات صارمة):")
    print("-" * 80)
    
    if final_weights:
        # ترتيب المؤشرات حسب الوزن
        sorted_indicators = sorted(final_weights.items(), key=lambda x: x[1]['weight'], reverse=True)
        
        for indicator, stats in sorted_indicators:
            print(f"\n🔹 {indicator}:")
            print(f"   الوزن: {stats['weight']:.4f}")
            print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
            print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
            print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
            print(f"   نسبة شارب: {stats['sharpe_ratio']:.3f}")
            print(f"   إجمالي الإشارات: {stats['total_signals']:,}")
            print(f"   الإشارات الناجحة: {stats['successful_signals']:,}")
        
        print(f"\n📊 ملخص:")
        print(f"   المؤشرات المقبولة: {len(final_weights)}/5")
        print(f"   إجمالي الأوزان: {sum(v['weight'] for v in final_weights.values()):.3f}")
    else:
        print("\n⚠️ لا توجد مؤشرات تحقق العتبات الصارمة!")
        print("   العتبات: احتمالية ≥60%, عائد ≥10%")
    
    print(f"\n📁 تم حفظ الأوزان الذكية في {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
