import json
import pandas as pd
import ccxt
from datetime import datetime

def test_advanced_weights():
    """اختبار تحميل الأوزان المتقدمة"""
    try:
        with open('advanced_weights.json', 'r', encoding='utf-8') as f:
            weights = json.load(f)
        
        print("✅ تم تحميل الأوزان المتقدمة بنجاح")
        print(f"📊 عدد المؤشرات: {len(weights)}")
        
        # عرض أفضل 3 مؤشرات
        sorted_indicators = sorted(
            weights.items(), 
            key=lambda x: x[1].get('success_probability', 0), 
            reverse=True
        )
        
        print("\n🏆 أفضل 3 مؤشرات حسب احتمالية النجاح:")
        for i, (indicator, stats) in enumerate(sorted_indicators[:3], 1):
            print(f"{i}. {indicator}:")
            print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
            print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
            print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في تحميل الأوزان: {e}")
        return False

def test_prediction_engine():
    """اختبار محرك التنبؤ"""
    try:
        from crypto_monitor_advanced import PredictionEngine
        
        engine = PredictionEngine()
        
        # اختبار إشارات وهمية
        test_signals = {
            'rsi_bullish': True,
            'macd_bullish': True,
            'adx_bullish': False,
            'bollinger_breakout': True,
            'ema10_above_ema50': True,
            'volume_spike': False,
            'obv_bullish': True,
            'cmf_bullish': False,
            'stoch_rsi_bullish': True
        }
        
        metrics = engine.calculate_prediction_metrics(test_signals)
        
        print("✅ محرك التنبؤ يعمل بنجاح")
        print("📊 نتائج الاختبار:")
        print(f"   احتمالية الصعود: {metrics['probability']:.1%}")
        print(f"   العائد المتوقع: {metrics['expected_return']:+.2f}%")
        print(f"   الوقت المتوقع: {metrics['expected_time']:.1f} ساعة")
        print(f"   نقاط الثقة: {metrics['confidence_score']:.0f}/100")
        print(f"   مستوى المخاطر: {metrics['risk_level']}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في محرك التنبؤ: {e}")
        return False

def test_real_data_analysis():
    """اختبار التحليل على بيانات حقيقية"""
    try:
        # تحميل بيانات BTC للاختبار
        exchange = ccxt.binance()
        ohlcv = exchange.fetch_ohlcv('BTCUSDT', '4h', limit=100)
        
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        print("✅ تم تحميل بيانات BTC بنجاح")
        print(f"📊 عدد الشموع: {len(df)}")
        print(f"💰 السعر الحالي: ${df['close'].iloc[-1]:.2f}")
        print(f"📈 التغيير 24س: {((df['close'].iloc[-1] - df['close'].iloc[-6]) / df['close'].iloc[-6] * 100):+.2f}%")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في تحليل البيانات الحقيقية: {e}")
        return False

def display_system_overview():
    """عرض نظرة عامة على النظام"""
    print("🚀 نظام مراقبة العملات المشفرة المتقدم")
    print("=" * 60)
    
    features = [
        "✅ تحليل 9 مؤشرات فنية متقدمة",
        "✅ حساب احتمالية الصعود بدقة",
        "✅ تقدير العائد المتوقع",
        "✅ حساب المدة المتوقعة للهدف",
        "✅ تصنيف مستوى المخاطر",
        "✅ نظام cooldown ذكي",
        "✅ توصيات تداول مخصصة",
        "✅ تنبيهات تليجرام متقدمة"
    ]
    
    print("🎯 الميزات الرئيسية:")
    for feature in features:
        print(f"   {feature}")
    
    print(f"\n📊 الإحصائيات من البيانات التاريخية:")
    try:
        with open('advanced_weights.json', 'r', encoding='utf-8') as f:
            weights = json.load(f)
        
        # حساب متوسط الإحصائيات
        probabilities = [stats['success_probability'] for stats in weights.values()]
        returns = [stats['average_return'] for stats in weights.values()]
        times = [stats['average_time_to_target'] for stats in weights.values()]
        
        print(f"   متوسط احتمالية النجاح: {sum(probabilities)/len(probabilities):.1%}")
        print(f"   متوسط العائد المتوقع: {sum(returns)/len(returns):+.2f}%")
        print(f"   متوسط الوقت للهدف: {sum(times)/len(times):.1f} فترات ({sum(times)/len(times)*4:.1f} ساعة)")
        
        # أفضل مؤشر
        best_indicator = max(weights.items(), key=lambda x: x[1]['success_probability'])
        print(f"   أفضل مؤشر: {best_indicator[0]} ({best_indicator[1]['success_probability']:.1%} نجاح)")
        
    except:
        print("   ⚠️ لم يتم العثور على الإحصائيات المتقدمة")

def generate_usage_example():
    """إنشاء مثال على الاستخدام"""
    print(f"\n💡 مثال على الاستخدام:")
    print("-" * 40)
    
    example_code = '''
# تشغيل النظام المتقدم
python crypto_monitor_advanced.py

# عرض التحليلات المتقدمة
python advanced_analytics.py

# اختبار النظام
python test_advanced_system.py
'''
    
    print(example_code)
    
    print("📋 خطوات التشغيل:")
    steps = [
        "1. تأكد من وجود ملف symbols.txt مع قائمة العملات",
        "2. قم بتشغيل 'statistics - New.py' لحساب الإحصائيات",
        "3. اضبط إعدادات تليجرام في متغيرات البيئة",
        "4. شغل 'crypto_monitor_advanced.py' للمراقبة المستمرة"
    ]
    
    for step in steps:
        print(f"   {step}")

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🧪 اختبار النظام المتقدم")
    print("=" * 50)
    
    tests = [
        ("تحميل الأوزان المتقدمة", test_advanced_weights),
        ("محرك التنبؤ", test_prediction_engine),
        ("تحليل البيانات الحقيقية", test_real_data_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
        print("-" * 30)
    
    print(f"\n📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للعمل")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه")
    
    # عرض نظرة عامة على النظام
    display_system_overview()
    generate_usage_example()

if __name__ == "__main__":
    main()
