#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام التحليل الذكي المخصص لكل عملة
يحلل كل عملة بناءً على خصائصها الفردية والبيانات التاريخية
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class SmartCryptoAnalyzer:
    """محلل ذكي مخصص لكل عملة"""
    
    def __init__(self):
        # قاعدة بيانات خصائص العملات
        self.crypto_profiles = {
            # العملات الكبيرة (تقلبات منخفضة)
            'BTC': {'volatility': 'low', 'typical_move': 0.05, 'max_target': 0.12, 'min_confidence': 0.75},
            'ETH': {'volatility': 'low', 'typical_move': 0.06, 'max_target': 0.15, 'min_confidence': 0.75},
            'BNB': {'volatility': 'medium', 'typical_move': 0.08, 'max_target': 0.18, 'min_confidence': 0.70},
            
            # العملات المتوسطة (تقلبات متوسطة)
            'ADA': {'volatility': 'medium', 'typical_move': 0.10, 'max_target': 0.25, 'min_confidence': 0.70},
            'XRP': {'volatility': 'medium', 'typical_move': 0.12, 'max_target': 0.30, 'min_confidence': 0.70},
            'SOL': {'volatility': 'medium', 'typical_move': 0.10, 'max_target': 0.25, 'min_confidence': 0.70},
            'DOT': {'volatility': 'medium', 'typical_move': 0.10, 'max_target': 0.25, 'min_confidence': 0.70},
            'MATIC': {'volatility': 'medium', 'typical_move': 0.12, 'max_target': 0.30, 'min_confidence': 0.70},
            'LINK': {'volatility': 'medium', 'typical_move': 0.10, 'max_target': 0.25, 'min_confidence': 0.70},
            'UNI': {'volatility': 'medium', 'typical_move': 0.12, 'max_target': 0.30, 'min_confidence': 0.70},
            'AVAX': {'volatility': 'medium', 'typical_move': 0.12, 'max_target': 0.30, 'min_confidence': 0.70},
            
            # العملات الصغيرة (تقلبات عالية)
            'SHIB': {'volatility': 'high', 'typical_move': 0.20, 'max_target': 0.50, 'min_confidence': 0.65},
            'DOGE': {'volatility': 'high', 'typical_move': 0.15, 'max_target': 0.40, 'min_confidence': 0.65},
            'MANA': {'volatility': 'high', 'typical_move': 0.15, 'max_target': 0.40, 'min_confidence': 0.65},
            'SAND': {'volatility': 'high', 'typical_move': 0.15, 'max_target': 0.40, 'min_confidence': 0.65},
        }
        
        print("🧠 نظام التحليل الذكي المخصص")
        print("📊 تحليل فردي لكل عملة")
        print("📈 أهداف ديناميكية حسب التقلبات")
    
    def get_crypto_profile(self, symbol):
        """الحصول على ملف العملة أو إنشاء ملف افتراضي"""
        if symbol in self.crypto_profiles:
            return self.crypto_profiles[symbol]
        
        # ملف افتراضي للعملات غير المعروفة
        return {
            'volatility': 'medium',
            'typical_move': 0.10,
            'max_target': 0.25,
            'min_confidence': 0.70
        }
    
    def analyze_historical_volatility(self, df, days=30):
        """تحليل التقلبات التاريخية"""
        try:
            if len(df) < days:
                return 0.10  # افتراضي
            
            # حساب التغيرات اليومية
            recent_data = df.tail(days)
            daily_returns = recent_data['close'].pct_change().dropna()
            
            # حساب الانحراف المعياري (التقلبات)
            volatility = daily_returns.std()
            
            return min(max(volatility, 0.02), 0.50)  # بين 2% و 50%
            
        except Exception as e:
            return 0.10  # افتراضي عند الخطأ
    
    def analyze_price_patterns(self, df, days=60):
        """تحليل أنماط الأسعار التاريخية"""
        try:
            if len(df) < days:
                return {'avg_move': 0.08, 'max_move': 0.20, 'success_rate': 0.5}
            
            recent_data = df.tail(days)
            
            # حساب الحركات الصاعدة
            daily_changes = recent_data['close'].pct_change().dropna()
            positive_moves = daily_changes[daily_changes > 0]
            
            if len(positive_moves) == 0:
                return {'avg_move': 0.08, 'max_move': 0.20, 'success_rate': 0.5}
            
            avg_move = positive_moves.mean()
            max_move = positive_moves.quantile(0.9)  # أفضل 10%
            success_rate = len(positive_moves) / len(daily_changes)
            
            return {
                'avg_move': min(max(avg_move, 0.02), 0.30),
                'max_move': min(max(max_move, 0.05), 0.60),
                'success_rate': min(max(success_rate, 0.3), 0.8)
            }
            
        except Exception as e:
            return {'avg_move': 0.08, 'max_move': 0.20, 'success_rate': 0.5}
    
    def analyze_volume_trend(self, df, days=14):
        """تحليل اتجاه الحجم"""
        try:
            if len(df) < days * 2:
                return 1.0  # محايد
            
            recent_volume = df['volume'].tail(days).mean()
            previous_volume = df['volume'].tail(days * 2).head(days).mean()
            
            if previous_volume == 0:
                return 1.0
            
            volume_ratio = recent_volume / previous_volume
            return min(max(volume_ratio, 0.5), 3.0)  # بين 0.5x و 3x
            
        except Exception as e:
            return 1.0
    
    def calculate_market_cap_factor(self, symbol):
        """عامل القيمة السوقية"""
        # العملات الكبيرة أكثر استقرار
        large_caps = ['BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'SOL', 'DOT']
        medium_caps = ['MATIC', 'LINK', 'UNI', 'AVAX', 'ATOM', 'NEAR', 'ICP']
        
        if symbol in large_caps:
            return 0.8  # أهداف أقل
        elif symbol in medium_caps:
            return 1.0  # أهداف عادية
        else:
            return 1.3  # أهداف أعلى للعملات الصغيرة
    
    def calculate_smart_target(self, symbol, df, prediction_confidence):
        """حساب الهدف الذكي لكل عملة"""
        try:
            # الحصول على ملف العملة
            profile = self.get_crypto_profile(symbol)
            
            # تحليل البيانات التاريخية
            volatility = self.analyze_historical_volatility(df)
            patterns = self.analyze_price_patterns(df)
            volume_factor = self.analyze_volume_trend(df)
            market_cap_factor = self.calculate_market_cap_factor(symbol)
            
            # حساب الهدف الأساسي
            base_target = profile['typical_move']
            
            # تعديل الهدف بناءً على التحليل
            # 1. التقلبات التاريخية
            volatility_adjustment = min(volatility * 2, 0.3)
            
            # 2. أنماط الأسعار
            pattern_adjustment = patterns['avg_move'] * 1.5
            
            # 3. ثقة النموذج
            confidence_boost = (prediction_confidence - 0.7) * 0.5 if prediction_confidence > 0.7 else 0
            
            # 4. اتجاه الحجم
            volume_boost = (volume_factor - 1) * 0.1
            
            # 5. عامل القيمة السوقية
            market_adjustment = market_cap_factor
            
            # حساب الهدف النهائي
            smart_target = (
                base_target * 0.3 +
                volatility_adjustment * 0.2 +
                pattern_adjustment * 0.3 +
                confidence_boost * 0.1 +
                volume_boost * 0.1
            ) * market_adjustment
            
            # تطبيق الحدود
            min_target = 0.03  # 3% كحد أدنى
            max_target = profile['max_target']
            
            final_target = min(max(smart_target, min_target), max_target)
            
            return {
                'target_return': final_target,
                'confidence_threshold': profile['min_confidence'],
                'analysis': {
                    'base_target': base_target,
                    'volatility': volatility,
                    'avg_historical_move': patterns['avg_move'],
                    'volume_factor': volume_factor,
                    'market_cap_factor': market_cap_factor,
                    'final_target': final_target
                }
            }
            
        except Exception as e:
            # قيم افتراضية عند الخطأ
            return {
                'target_return': 0.08,
                'confidence_threshold': 0.70,
                'analysis': {'error': str(e)}
            }
    
    def get_dynamic_confidence_threshold(self, symbol, df):
        """حساب عتبة الثقة الديناميكية"""
        try:
            profile = self.get_crypto_profile(symbol)
            patterns = self.analyze_price_patterns(df)
            
            # تعديل عتبة الثقة بناءً على معدل النجاح التاريخي
            base_threshold = profile['min_confidence']
            success_adjustment = (0.6 - patterns['success_rate']) * 0.2
            
            dynamic_threshold = base_threshold + success_adjustment
            
            # تطبيق الحدود
            return min(max(dynamic_threshold, 0.60), 0.85)
            
        except Exception as e:
            return 0.70  # افتراضي
    
    def format_analysis_summary(self, symbol, analysis):
        """تنسيق ملخص التحليل"""
        try:
            details = analysis.get('analysis', {})
            
            summary = f"""
📊 تحليل ذكي لـ {symbol}:
🎯 الهدف المحسوب: {analysis['target_return']:.1%}
📈 التقلبات التاريخية: {details.get('volatility', 0):.1%}
📊 متوسط الحركة: {details.get('avg_historical_move', 0):.1%}
📦 عامل الحجم: {details.get('volume_factor', 1):.2f}x
🏦 عامل القيمة السوقية: {details.get('market_cap_factor', 1):.2f}x
🎯 عتبة الثقة: {analysis['confidence_threshold']:.1%}
            """
            
            return summary.strip()
            
        except Exception as e:
            return f"❌ خطأ في تنسيق التحليل: {e}"

def main():
    """اختبار النظام"""
    analyzer = SmartCryptoAnalyzer()
    
    # اختبار بعض العملات
    test_symbols = ['BTC', 'ETH', 'SHIB', 'DOGE', 'UNKNOWN']
    
    for symbol in test_symbols:
        # محاكاة بيانات للاختبار
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = 100 * np.cumprod(1 + np.random.normal(0, 0.02, 100))
        volumes = np.random.uniform(1000000, 10000000, 100)
        
        df = pd.DataFrame({
            'close': prices,
            'volume': volumes
        }, index=dates)
        
        # تحليل العملة
        analysis = analyzer.calculate_smart_target(symbol, df, 0.85)
        
        print(f"\n{analyzer.format_analysis_summary(symbol, analysis)}")

if __name__ == "__main__":
    main()
