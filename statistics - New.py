import os
import pandas as pd
import json
import numpy as np
from ta.volume import OnBalanceVolumeIndicator, ChaikinMoneyFlowIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.trend import MACD, EMAIndicator, ADXIndicator
from ta.volatility import BollingerBands

DATA_DIR = 'data'
OUTPUT_FILE = 'weights.json'

INDICATORS = [
    'rsi_bullish',
    'macd_bullish',
    'adx_bullish',
    'bollinger_breakout',
    'ema10_above_ema50',
    'volume_spike',
    'obv_bullish',
    'cmf_bullish',
    'stoch_rsi_bullish'
]

def detect_bullish_signals(df):
    signals = dict.fromkeys(INDICATORS, False)

    # التأكد من وجود بيانات كافية
    if len(df) < 5:
        return signals

    # RSI - تحسين المعايير
    rsi_current = df['rsi'].iloc[-1]
    signals['rsi_bullish'] = 30 <= rsi_current <= 70  # منطقة صحية للشراء

    # MACD - تحسين المعايير
    macd_current = df['macd'].iloc[-1]
    macd_signal_current = df['macd_signal'].iloc[-1]
    macd_prev = df['macd'].iloc[-2] if len(df) > 1 else macd_current
    macd_signal_prev = df['macd_signal'].iloc[-2] if len(df) > 1 else macd_signal_current

    # إشارة صعود: MACD يعبر فوق الإشارة أو أعلى منها مع تحسن
    signals['macd_bullish'] = (macd_current > macd_signal_current and
                              (macd_current - macd_signal_current) > (macd_prev - macd_signal_prev))

    # ADX - قوة الاتجاه
    signals['adx_bullish'] = df['adx'].iloc[-1] > 25  # اتجاه قوي

    # Bollinger breakout - اختراق مع حجم
    close_current = df['close'].iloc[-1]
    bb_upper_current = df['bb_upper'].iloc[-1]
    bb_lower_current = df['bb_lower'].iloc[-1]
    bb_middle = (bb_upper_current + bb_lower_current) / 2

    # اختراق قوي للحد العلوي
    signals['bollinger_breakout'] = close_current > bb_upper_current

    # EMA 20 > EMA 50 مع تأكيد الاتجاه
    ema20_current = df['ema20'].iloc[-1]
    ema50_current = df['ema50'].iloc[-1]
    ema20_prev = df['ema20'].iloc[-2] if len(df) > 1 else ema20_current
    ema50_prev = df['ema50'].iloc[-2] if len(df) > 1 else ema50_current

    signals['ema10_above_ema50'] = (ema20_current > ema50_current and
                                   (ema20_current - ema50_current) > (ema20_prev - ema50_prev))

    # Volume Spike - تحسين الحساب
    if len(df) >= 21:
        avg_volume = df['volume'].iloc[-21:-1].mean()
        signals['volume_spike'] = df['volume'].iloc[-1] > 1.5 * avg_volume
    else:
        signals['volume_spike'] = False

    # OBV bullish
    signals['obv_bullish'] = df['obv'].iloc[-1] > df['obv'].iloc[-5]

    # CMF
    signals['cmf_bullish'] = df['cmf'].iloc[-1] > 0

    # Stochastic RSI crossover (تصحيح أسماء الأعمدة)
    k = df['stoch_rsi_k'].iloc[-1]
    d = df['stoch_rsi_d'].iloc[-1]
    signals['stoch_rsi_bullish'] = k > d and k < 80 and d < 80

    return signals

def calculate_indicators(df):
    df = df.copy()
    df['rsi'] = RSIIndicator(close=df['close']).rsi()

    macd = MACD(close=df['close'])
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()

    df['ema20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()
    df['ema50'] = EMAIndicator(close=df['close'], window=50).ema_indicator()

    adx = ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
    df['adx'] = adx.adx()

    bb = BollingerBands(close=df['close'])
    df['bb_upper'] = bb.bollinger_hband()
    df['bb_lower'] = bb.bollinger_lband()

    df['obv'] = OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
    df['cmf'] = ChaikinMoneyFlowIndicator(high=df['high'], low=df['low'], close=df['close'], volume=df['volume']).chaikin_money_flow()

    # استخدام Stochastic RSI بدلاً من Stochastic العادي لمطابقة البيانات
    from ta.momentum import StochRSIIndicator
    stoch_rsi = StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
    df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
    df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()

    return df

def analyze_file(filepath):
    try:
        df = pd.read_csv(filepath)
        if df.shape[0] < 60:
            return None

        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)

        df = calculate_indicators(df)
        df.dropna(inplace=True)

        # إحصائيات متقدمة
        signal_stats = {}
        for indicator in INDICATORS:
            signal_stats[indicator] = {
                'total_signals': 0,
                'successful_signals': 0,
                'total_return': 0,
                'time_to_targets': [],
                'returns': []
            }

        # تحليل فترات مختلفة للتنبؤ
        for i in range(55, len(df) - 12):  # زيادة النطاق للتحليل
            window = df.iloc[i-5:i+1]
            if window.isnull().values.any():
                continue

            signals = detect_bullish_signals(window)
            current_close = df['close'].iloc[i]

            # فحص النتائج لفترات مختلفة (1, 3, 6, 12 فترات)
            for periods in [1, 3, 6, 12]:
                if i + periods < len(df):
                    future_close = df['close'].iloc[i + periods]
                    price_change = (future_close - current_close) / current_close

                    # تسجيل الإحصائيات لكل مؤشر
                    for indicator, is_active in signals.items():
                        if is_active:
                            signal_stats[indicator]['total_signals'] += 1
                            signal_stats[indicator]['returns'].append(price_change * 100)

                            # معايير نجاح متدرجة حسب الفترة الزمنية
                            success_threshold = 0.015 + (periods * 0.005)  # 1.5% + 0.5% لكل فترة
                            if price_change > success_threshold:
                                signal_stats[indicator]['successful_signals'] += 1
                                signal_stats[indicator]['time_to_targets'].append(periods)

                            signal_stats[indicator]['total_return'] += price_change * 100

        return signal_stats

    except Exception as e:
        print(f"❌ خطأ في تحليل الملف {filepath}: {e}")
        return None

def main():
    # إحصائيات شاملة لجميع المؤشرات
    all_stats = {}
    for indicator in INDICATORS:
        all_stats[indicator] = {
            'total_signals': 0,
            'successful_signals': 0,
            'total_return': 0,
            'time_to_targets': [],
            'returns': []
        }

    files_processed = 0

    for filename in os.listdir(DATA_DIR):
        if not filename.endswith('.csv'):
            continue
        filepath = os.path.join(DATA_DIR, filename)
        print(f"🔍 تحليل: {filename}")
        file_stats = analyze_file(filepath)
        if file_stats is None:
            continue

        # دمج الإحصائيات
        for indicator in INDICATORS:
            if indicator in file_stats:
                all_stats[indicator]['total_signals'] += file_stats[indicator]['total_signals']
                all_stats[indicator]['successful_signals'] += file_stats[indicator]['successful_signals']
                all_stats[indicator]['total_return'] += file_stats[indicator]['total_return']
                all_stats[indicator]['time_to_targets'].extend(file_stats[indicator]['time_to_targets'])
                all_stats[indicator]['returns'].extend(file_stats[indicator]['returns'])

        files_processed += 1

    # حساب الإحصائيات المتقدمة
    advanced_weights = {}

    for indicator in INDICATORS:
        stats = all_stats[indicator]

        # حساب احتمالية النجاح
        success_rate = (stats['successful_signals'] / stats['total_signals']) if stats['total_signals'] > 0 else 0

        # حساب متوسط العائد
        avg_return = np.mean(stats['returns']) if stats['returns'] else 0

        # حساب متوسط الوقت للهدف
        avg_time_to_target = np.mean(stats['time_to_targets']) if stats['time_to_targets'] else 0

        # حساب الانحراف المعياري للعوائد
        return_std = np.std(stats['returns']) if len(stats['returns']) > 1 else 0

        # حساب نسبة شارب المبسطة (العائد / المخاطر)
        sharpe_ratio = (avg_return / return_std) if return_std > 0 else 0

        # حساب الوزن بطريقة علمية محسنة
        # الوزن = (احتمالية النجاح × العائد المتوسط × نسبة شارب) / عدد الإشارات المعياري
        base_weight = success_rate * max(0, avg_return) * max(0, sharpe_ratio)

        # تطبيع الوزن بناءً على عدد الإشارات (مؤشرات بإشارات أكثر تحصل على وزن أعلى)
        signal_factor = min(1.0, stats['total_signals'] / 1000)  # تطبيع لـ 1000 إشارة

        # الوزن النهائي
        final_weight = base_weight * signal_factor

        advanced_weights[indicator] = {
            'success_probability': round(success_rate, 3),
            'average_return': round(avg_return, 3),
            'average_time_to_target': round(avg_time_to_target, 1),
            'return_volatility': round(return_std, 3),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'total_signals': stats['total_signals'],
            'successful_signals': stats['successful_signals'],
            'weight': round(max(0, final_weight), 4)  # وزن محسن علمياً
        }

    # فلترة المؤشرات الضعيفة وتطبيع الأوزان
    filtered_weights = {}
    for indicator, stats in advanced_weights.items():
        # فلترة المؤشرات بناءً على معايير الجودة
        if (stats['total_signals'] >= 100 and  # حد أدنى من الإشارات
            stats['success_probability'] >= 0.1 and  # حد أدنى من النجاح
            stats['weight'] > 0):  # وزن إيجابي
            filtered_weights[indicator] = stats

    # حفظ الأوزان المتقدمة المفلترة
    with open('advanced_weights.json', 'w', encoding='utf-8') as f:
        json.dump(filtered_weights, f, indent=4, ensure_ascii=False)

    # حفظ الأوزان البسيطة للتوافق مع النظام القديم
    simple_weights = {k: v['weight'] for k, v in filtered_weights.items()}
    total_weight = sum(simple_weights.values())
    if total_weight > 0:
        simple_weights = {k: round(v / total_weight, 3) for k, v in simple_weights.items()}

    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(simple_weights, f, indent=4, ensure_ascii=False)

    print(f"\n✅ تم تحليل {files_processed} ملف")
    print(f"📊 إحصائيات المؤشرات المفلترة:")
    print("-" * 80)

    # ترتيب المؤشرات حسب الوزن
    sorted_indicators = sorted(filtered_weights.items(), key=lambda x: x[1]['weight'], reverse=True)

    for indicator, stats in sorted_indicators:
        print(f"\n🔹 {indicator}:")
        print(f"   الوزن: {stats['weight']:.4f}")
        print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
        print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
        print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
        print(f"   نسبة شارب: {stats['sharpe_ratio']:.3f}")
        print(f"   إجمالي الإشارات: {stats['total_signals']:,}")
        print(f"   الإشارات الناجحة: {stats['successful_signals']:,}")

    print(f"\n📊 ملخص:")
    print(f"   المؤشرات المقبولة: {len(filtered_weights)}/{len(INDICATORS)}")
    print(f"   إجمالي الأوزان: {sum(simple_weights.values()):.3f}")

    print(f"\n📁 تم حفظ الأوزان المتقدمة في advanced_weights.json")
    print(f"📁 تم حفظ الأوزان البسيطة في {OUTPUT_FILE}")

    if len(filtered_weights) == 0:
        print("\n⚠️ تحذير: لم يتم قبول أي مؤشر! تحقق من البيانات والمعايير.")

if __name__ == "__main__":
    main()
