import os
import pandas as pd
import json
from ta.volume import OnBalanceVolumeIndicator, ChaikinMoneyFlowIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.trend import MACD, EMAIndicator, ADXIndicator
from ta.volatility import BollingerBands

DATA_DIR = 'data'
OUTPUT_FILE = 'weights.json'

INDICATORS = [
    'rsi_bullish',
    'macd_bullish',
    'adx_bullish',
    'bollinger_breakout',
    'ema10_above_ema50',
    'volume_spike',
    'obv_bullish',
    'cmf_bullish',
    'stoch_rsi_bullish'
]

def detect_bullish_signals(df):
    signals = dict.fromkeys(INDICATORS, False)

    # RSI
    signals['rsi_bullish'] = df['rsi'].iloc[-1] > 50

    # MACD
    signals['macd_bullish'] = df['macd'].iloc[-1] > df['macd_signal'].iloc[-1]

    # ADX
    signals['adx_bullish'] = df['adx'].iloc[-1] > 20

    # Bollinger breakout
    signals['bollinger_breakout'] = df['close'].iloc[-1] > df['bb_upper'].iloc[-1]

    # EMA 20 > EMA 50 (تصحيح لمطابقة البيانات المحفوظة)
    signals['ema10_above_ema50'] = df['ema20'].iloc[-1] > df['ema50'].iloc[-1]

    # Volume Spike
    avg_volume = df['volume'].iloc[-21:-1].mean()
    signals['volume_spike'] = df['volume'].iloc[-1] > 1.5 * avg_volume

    # OBV bullish
    signals['obv_bullish'] = df['obv'].iloc[-1] > df['obv'].iloc[-5]

    # CMF
    signals['cmf_bullish'] = df['cmf'].iloc[-1] > 0

    # Stochastic RSI crossover (تصحيح أسماء الأعمدة)
    k = df['stoch_rsi_k'].iloc[-1]
    d = df['stoch_rsi_d'].iloc[-1]
    signals['stoch_rsi_bullish'] = k > d and k < 80 and d < 80

    return signals

def calculate_indicators(df):
    df = df.copy()
    df['rsi'] = RSIIndicator(close=df['close']).rsi()

    macd = MACD(close=df['close'])
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()

    df['ema20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()
    df['ema50'] = EMAIndicator(close=df['close'], window=50).ema_indicator()

    adx = ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
    df['adx'] = adx.adx()

    bb = BollingerBands(close=df['close'])
    df['bb_upper'] = bb.bollinger_hband()
    df['bb_lower'] = bb.bollinger_lband()

    df['obv'] = OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
    df['cmf'] = ChaikinMoneyFlowIndicator(high=df['high'], low=df['low'], close=df['close'], volume=df['volume']).chaikin_money_flow()

    # استخدام Stochastic RSI بدلاً من Stochastic العادي لمطابقة البيانات
    from ta.momentum import StochRSIIndicator
    stoch_rsi = StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
    df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
    df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()

    return df

def analyze_file(filepath):
    try:
        df = pd.read_csv(filepath)
        if df.shape[0] < 60:
            return None

        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)

        df = calculate_indicators(df)
        df.dropna(inplace=True)

        signal_counts = dict.fromkeys(INDICATORS, 0)

        for i in range(55, len(df) - 3):
            current_close = df['close'].iloc[i]
            future_close = df['close'].iloc[i + 3]
            price_change = (future_close - current_close) / current_close

            if price_change > 0.03:
                window = df.iloc[i-5:i+1]
                if window.isnull().values.any():
                    continue
                signals = detect_bullish_signals(window)
                for k, v in signals.items():
                    if v:
                        signal_counts[k] += 1

        return signal_counts

    except Exception as e:
        print(f"❌ خطأ في تحليل الملف {filepath}: {e}")
        return None

def main():
    all_counts = dict.fromkeys(INDICATORS, 0)
    files_processed = 0

    for filename in os.listdir(DATA_DIR):
        if not filename.endswith('.csv'):
            continue
        filepath = os.path.join(DATA_DIR, filename)
        print(f"🔍 تحليل: {filename}")
        counts = analyze_file(filepath)
        if counts is None:
            continue
        for k in INDICATORS:
            all_counts[k] += counts[k]
        files_processed += 1

    total_signals = sum(all_counts.values())
    weights = {k: round(v / total_signals, 3) if total_signals > 0 else 0.0 for k, v in all_counts.items()}

    with open(OUTPUT_FILE, 'w') as f:
        json.dump(weights, f, indent=4, ensure_ascii=False)

    print(f"\n✅ تم تحليل {files_processed} ملف وحساب الأوزان بناءً على {total_signals} حالة صعود قوية.")
    print(f"📁 تم حفظ الأوزان في {OUTPUT_FILE}:\n")
    print(json.dumps(weights, indent=4, ensure_ascii=False))

if __name__ == "__main__":
    main()
