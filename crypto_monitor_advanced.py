import ccxt
import pandas as pd
import ta
import requests
import time
import os
import json
import numpy as np
from datetime import datetime, timedelta
import logging

# === إعداد السجلات ===
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# === إعداد تليجرام ===
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN", "**********************************************")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID", "766367805")

# === إعدادات النظام ===
TIMEFRAME = '4h'
LIMIT = 100
MIN_PROBABILITY_THRESHOLD = 0.20  # تخفيض من 0.55 إلى 0.20 (20% احتمالية نجاح)
MIN_EXPECTED_RETURN = 1.0  # تخفيض من 5.0 إلى 1.0 (1% عائد متوقع كحد أدنى)
COOLDOWN_HOURS = 12

# === متغيرات عامة ===
last_alerts = {}
symbol_predictions = {}
advanced_weights = {}

def load_advanced_weights():
    """تحميل الأوزان المتقدمة"""
    global advanced_weights
    try:
        with open('realistic_final_weights.json', 'r', encoding='utf-8') as f:
            advanced_weights = json.load(f)
        logger.info("✅ تم تحميل الأوزان المتقدمة")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الأوزان: {e}")
        return False

def send_telegram(message):
    """إرسال رسالة تليجرام"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    data = {'chat_id': CHAT_ID, 'text': message, 'parse_mode': 'Markdown'}
    try:
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("✅ تم إرسال التنبيه")
        else:
            logger.error(f"❌ فشل إرسال التنبيه: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ خطأ في إرسال التنبيه: {e}")

def load_symbols(filename="symbols.txt"):
    """تحميل قائمة العملات"""
    if not os.path.exists(filename):
        logger.error(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                # الاحتفاظ بالتنسيق الأصلي للعملة (مع الشرطة المائلة)
                symbols.append(line)
        return symbols

def calculate_indicators(df):
    """حساب المؤشرات الفنية"""
    try:
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
        
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        
        df['ema20'] = ta.trend.EMAIndicator(close=df['close'], window=20).ema_indicator()
        df['ema50'] = ta.trend.EMAIndicator(close=df['close'], window=50).ema_indicator()
        
        adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        df['adx'] = adx.adx()
        
        bb = ta.volatility.BollingerBands(close=df['close'])
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_lower'] = bb.bollinger_lband()
        
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'], low=df['low'], close=df['close'], volume=df['volume']
        ).chaikin_money_flow()
        
        stoch_rsi = ta.momentum.StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
        df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
        df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
        
        df['volume_avg_20'] = df['volume'].rolling(window=20).mean()
        df['volume_spike_ratio'] = df['volume'] / df['volume_avg_20']
        
        return df
    except Exception as e:
        logger.error(f"❌ خطأ في حساب المؤشرات: {e}")
        return None

def detect_signals(df):
    """كشف الإشارات الواقعية للمؤشرات الخمسة"""
    if df is None or len(df) < 10:
        return None, []

    signals = {}
    active_signals = []

    try:
        # RSI عالي الجودة
        if 'rsi' in df.columns and len(df) > 2:
            current_rsi = df['rsi'].iloc[-1]
            prev_rsi = df['rsi'].iloc[-2]
            prev2_rsi = df['rsi'].iloc[-3]
            signals['rsi'] = (
                35 <= current_rsi <= 65 and     # منطقة صحية
                current_rsi > prev_rsi > prev2_rsi  # تحسن مستمر
            )
            if signals['rsi']:
                active_signals.append(f"RSI قوي: {current_rsi:.1f}")
        else:
            signals['rsi'] = False

        # MACD عالي الجودة
        if 'macd' in df.columns and 'macd_signal' in df.columns and len(df) > 2:
            current_macd = df['macd'].iloc[-1]
            current_signal = df['macd_signal'].iloc[-1]
            prev_macd = df['macd'].iloc[-2]
            prev_signal = df['macd_signal'].iloc[-2]

            # تقاطع إيجابي قوي أو تحسن مستمر فوق الصفر
            bullish_cross = (current_macd > current_signal and prev_macd <= prev_signal)
            strong_uptrend = (current_macd > 0 and current_macd > prev_macd and current_macd > current_signal)

            signals['macd'] = bullish_cross or strong_uptrend
            if signals['macd']:
                active_signals.append("MACD قوي")
        else:
            signals['macd'] = False

        # OBV عالي الجودة
        if 'obv' in df.columns and len(df) >= 5:
            obv_values = df['obv'].iloc[-5:].values
            # اتجاه صاعد مستمر في آخر 5 فترات
            signals['obv'] = all(obv_values[i] < obv_values[i+1] for i in range(3))
            if signals['obv']:
                active_signals.append("OBV قوي ومستمر")
        else:
            signals['obv'] = False

        # ADX عالي الجودة (إذا متوفر)
        if 'adx' in df.columns and len(df) > 1:
            current_adx = df['adx'].iloc[-1]
            prev_adx = df['adx'].iloc[-2]

            signals['adx'] = (
                current_adx > 25 and           # قوة اتجاه عالية
                current_adx > prev_adx         # قوة متزايدة
            )
            if signals['adx']:
                active_signals.append(f"ADX قوي جداً: {current_adx:.1f}")
        else:
            signals['adx'] = False

        # CMF عالي الجودة (إذا متوفر)
        if 'cmf' in df.columns and len(df) >= 3:
            cmf_values = df['cmf'].iloc[-3:].values
            # تدفق نقدي إيجابي قوي ومستمر
            signals['cmf'] = all(val > 0.1 for val in cmf_values)
            if signals['cmf']:
                active_signals.append(f"CMF قوي: {cmf_values[-1]:.3f}")
        else:
            signals['cmf'] = False

        return signals, active_signals

    except Exception as e:
        logger.error(f"❌ خطأ في كشف الإشارات الواقعية: {e}")
        return None, []

def calculate_prediction_metrics(signals):
    """حساب مقاييس التنبؤ"""
    if not signals or not advanced_weights:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    # تحديد المؤشرات النشطة الفردية
    active_indicators = [indicator for indicator, is_active in signals.items() if is_active]
    
    if not active_indicators:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    # إضافة المؤشرات المركبة النشطة
    all_indicators = active_indicators.copy()
    
    # إضافة المؤشرات الثنائية
    for i in range(len(active_indicators)):
        for j in range(i+1, len(active_indicators)):
            combo_key = f"{active_indicators[i]} + {active_indicators[j]}"
            if combo_key in advanced_weights:
                all_indicators.append(combo_key)
    
    # إضافة المؤشرات الثلاثية
    for i in range(len(active_indicators)):
        for j in range(i+1, len(active_indicators)):
            for k in range(j+1, len(active_indicators)):
                combo_key = f"{active_indicators[i]} + {active_indicators[j]} + {active_indicators[k]}"
                if combo_key in advanced_weights:
                    all_indicators.append(combo_key)
    
    total_weight = 0
    weighted_probability = 0
    weighted_return = 0
    weighted_time = 0
    
    for indicator in all_indicators:
        if indicator in advanced_weights:
            stats = advanced_weights[indicator]
            weight = stats.get('weight', 0)
            
            total_weight += weight
            weighted_probability += stats.get('success_probability', 0) * weight
            weighted_return += stats.get('average_return', 0) * weight
            weighted_time += stats.get('average_time_to_target', 3) * weight  # قيمة افتراضية 3 إذا لم تكن موجودة
    
    if total_weight == 0:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    # حساب المتوسطات المرجحة
    probability = weighted_probability / total_weight
    expected_return = weighted_return / total_weight * 100  # تحويل إلى نسبة مئوية
    expected_time = weighted_time / total_weight
    
    # استخدام len(all_indicators) بدلاً من len(active_indicators) ليشمل الإشارات المركبة
    # الرقم 9 يستخدم للموائمة مع رسالة التلغرام التي تعرض (عدد الإشارات / 9)
    confidence_score = min(100, (probability * 0.4 + (expected_return / 10) * 0.3 +
                               (len(all_indicators) / 9) * 0.3) * 100)
    
    if confidence_score >= 80:
        risk_level = 'منخفض'
    elif confidence_score >= 60:
        risk_level = 'متوسط'
    else:
        risk_level = 'عالي'
    
    return {
        'probability': probability,
        'expected_return': expected_return,
        'expected_time': expected_time * 4,  # تحويل إلى ساعات
        'confidence_score': confidence_score,
        'risk_level': risk_level,
        'active_indicators_count': len(all_indicators)
    }

def analyze_symbol(symbol):
    """تحليل عملة واحدة"""
    exchange = ccxt.binance()

    try:
        ohlcv = exchange.fetch_ohlcv(symbol, TIMEFRAME, limit=LIMIT)
        if not ohlcv or len(ohlcv) < 50:
            logger.warning(f"⚠️ بيانات غير كافية لـ {symbol}")
            return
        
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        df = calculate_indicators(df)
        if df is None:
            return
        
        signals, active_signals = detect_signals(df)
        if signals is None:
            return
        
        metrics = calculate_prediction_metrics(signals)
        
        if (metrics['probability'] >= MIN_PROBABILITY_THRESHOLD and 
            metrics['expected_return'] >= MIN_EXPECTED_RETURN):
            
            # التحقق من فترة التهدئة لمنع إرسال تنبيهات متكررة
            now = datetime.now()
            if symbol in last_alerts and now < last_alerts[symbol] + timedelta(hours=COOLDOWN_HOURS):
                logger.info(f"⏳ {symbol} في فترة التهدئة، تم تخطي التنبيه.")
                return
            
            last_alerts[symbol] = now

            current_price = df['close'].iloc[-1]
            predicted_price = current_price * (1 + metrics['expected_return'] / 100)
            
            message = f"🚨 *إشارة صاعدة جديدة*\n\n"
            message += f"💰 *العملة:* {symbol}\n"
            message += f"💵 *السعر الحالي:* ${current_price:.6f}\n"
            message += f"🔮 *السعر المتوقع (48س):* ${predicted_price:.6f}\n\n"
            message += f"🎯 *احتمالية الصعود:* {metrics['probability']:.1%}\n"
            message += f"📊 *العائد المتوقع:* {metrics['expected_return']:+.2f}%\n"
            message += f"⏰ *المدة المتوقعة:* {metrics['expected_time']:.1f} ساعة\n"
            message += f"🔒 *نقاط الثقة:* {metrics['confidence_score']:.0f}/100\n"
            message += f"⚠️ *مستوى المخاطر:* {metrics['risk_level']}\n\n"
            
            if active_signals:
                message += f"✅ *الإشارات النشطة* ({metrics['active_indicators_count']}/9):\n"
                for signal in active_signals:
                    message += f"• {signal}\n"
            
            message += f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            logger.info(f"📤 إرسال تنبيه لـ {symbol}")
            send_telegram(message)
        else:
            logger.info(f"📊 {symbol}: احتمالية {metrics['probability']:.1%}, عائد {metrics['expected_return']:+.2f}% - تحت العتبة")

    except Exception as e:
        logger.error(f"❌ خطأ في تحليل {symbol}: {e}")

def main():
    """الوظيفة الرئيسية"""
    logger.info("🚀 بدء تشغيل نظام مراقبة العملات")

    if not load_advanced_weights():
        logger.error("❌ فشل في تحميل الأوزان")
        return

    logger.info(f"🔧 العتبات: احتمالية {MIN_PROBABILITY_THRESHOLD:.1%}, عائد {MIN_EXPECTED_RETURN}%")
    
    symbols = load_symbols()
    if not symbols:
        logger.error("❌ لا توجد عملات لمراقبتها")
        return
    
    logger.info(f"📋 تم تحميل {len(symbols)} عملة للمراقبة")
    
    try:
        while True:
            logger.info(f"🔄 بدء دورة جديدة - فحص {len(symbols)} عملة")

            for i, symbol in enumerate(symbols, 1):
                logger.info(f"🔍 [{i}/{len(symbols)}] فحص {symbol}")
                analyze_symbol(symbol)
                time.sleep(2)
            
            logger.info("✅ انتهت الدورة - انتظار 15 دقيقة")
            time.sleep(900)
            
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف النظام")
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    main()
