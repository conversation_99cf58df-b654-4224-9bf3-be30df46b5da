import ccxt
import pandas as pd
import ta
import requests
import time
import os
import json
import numpy as np
from datetime import datetime, timedelta
import logging

# === إعداد السجلات ===
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# === إعداد تليجرام ===
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN", "**********************************************")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID", "766367805")

# === إعدادات النظام ===
TIMEFRAME = '4h'
LIMIT = 100
MIN_PROBABILITY_THRESHOLD = 0.65
MIN_EXPECTED_RETURN = 3.0
COOLDOWN_HOURS = 12

# === متغيرات عامة ===
last_alerts = {}
symbol_predictions = {}
advanced_weights = {}

def load_advanced_weights():
    """تحميل الأوزان المتقدمة"""
    global advanced_weights
    try:
        with open('advanced_weights.json', 'r', encoding='utf-8') as f:
            advanced_weights = json.load(f)
        logger.info("✅ تم تحميل الأوزان المتقدمة")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الأوزان: {e}")
        return False

def send_telegram(message):
    """إرسال رسالة تليجرام"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    data = {'chat_id': CHAT_ID, 'text': message, 'parse_mode': 'Markdown'}
    try:
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("✅ تم إرسال التنبيه")
        else:
            logger.error(f"❌ فشل إرسال التنبيه: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ خطأ في إرسال التنبيه: {e}")

def load_symbols(filename="symbols.txt"):
    """تحميل قائمة العملات"""
    if not os.path.exists(filename):
        logger.error(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                symbol = line.replace("/", "")
                symbols.append(symbol)
        return symbols

def calculate_indicators(df):
    """حساب المؤشرات الفنية"""
    try:
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
        
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        
        df['ema20'] = ta.trend.EMAIndicator(close=df['close'], window=20).ema_indicator()
        df['ema50'] = ta.trend.EMAIndicator(close=df['close'], window=50).ema_indicator()
        
        adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        df['adx'] = adx.adx()
        
        bb = ta.volatility.BollingerBands(close=df['close'])
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_lower'] = bb.bollinger_lband()
        
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'], low=df['low'], close=df['close'], volume=df['volume']
        ).chaikin_money_flow()
        
        stoch_rsi = ta.momentum.StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
        df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
        df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
        
        df['volume_avg_20'] = df['volume'].rolling(window=20).mean()
        df['volume_spike_ratio'] = df['volume'] / df['volume_avg_20']
        
        return df
    except Exception as e:
        logger.error(f"❌ خطأ في حساب المؤشرات: {e}")
        return None

def detect_signals(df):
    """كشف الإشارات الصاعدة"""
    if df is None or len(df) < 50:
        return None, []
    
    signals = {}
    active_signals = []
    
    try:
        # RSI
        rsi_val = df['rsi'].iloc[-1]
        signals['rsi_bullish'] = 30 <= rsi_val <= 70
        if signals['rsi_bullish']:
            active_signals.append(f"RSI: {rsi_val:.1f}")
        
        # MACD
        macd_val = df['macd'].iloc[-1]
        macd_signal_val = df['macd_signal'].iloc[-1]
        signals['macd_bullish'] = macd_val > macd_signal_val
        if signals['macd_bullish']:
            active_signals.append("MACD إيجابي")
        
        # ADX
        adx_val = df['adx'].iloc[-1]
        signals['adx_bullish'] = adx_val > 25
        if signals['adx_bullish']:
            active_signals.append(f"ADX: {adx_val:.1f}")
        
        # Bollinger
        close_val = df['close'].iloc[-1]
        bb_upper_val = df['bb_upper'].iloc[-1]
        signals['bollinger_breakout'] = close_val > bb_upper_val
        if signals['bollinger_breakout']:
            active_signals.append("اختراق بولينجر")
        
        # EMA
        ema20_val = df['ema20'].iloc[-1]
        ema50_val = df['ema50'].iloc[-1]
        signals['ema10_above_ema50'] = ema20_val > ema50_val
        if signals['ema10_above_ema50']:
            active_signals.append("EMA20 > EMA50")
        
        # Volume
        volume_ratio = df['volume_spike_ratio'].iloc[-1]
        signals['volume_spike'] = volume_ratio > 1.5
        if signals['volume_spike']:
            active_signals.append(f"حجم مرتفع: {volume_ratio:.1f}x")
        
        # OBV
        obv_current = df['obv'].iloc[-1]
        obv_prev = df['obv'].iloc[-5] if len(df) >= 5 else df['obv'].iloc[0]
        signals['obv_bullish'] = obv_current > obv_prev
        if signals['obv_bullish']:
            active_signals.append("OBV صاعد")
        
        # CMF
        cmf_val = df['cmf'].iloc[-1]
        signals['cmf_bullish'] = cmf_val > 0
        if signals['cmf_bullish']:
            active_signals.append(f"CMF: {cmf_val:.3f}")
        
        # Stochastic RSI
        stoch_k = df['stoch_rsi_k'].iloc[-1]
        stoch_d = df['stoch_rsi_d'].iloc[-1]
        signals['stoch_rsi_bullish'] = stoch_k > stoch_d and stoch_k < 0.8
        if signals['stoch_rsi_bullish']:
            active_signals.append("StochRSI إيجابي")
        
        return signals, active_signals
        
    except Exception as e:
        logger.error(f"❌ خطأ في كشف الإشارات: {e}")
        return None, []

def calculate_prediction_metrics(signals):
    """حساب مقاييس التنبؤ"""
    if not signals or not advanced_weights:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    active_indicators = [indicator for indicator, is_active in signals.items() if is_active]
    
    if not active_indicators:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    total_weight = 0
    weighted_probability = 0
    weighted_return = 0
    weighted_time = 0
    
    for indicator in active_indicators:
        if indicator in advanced_weights:
            stats = advanced_weights[indicator]
            weight = stats.get('weight', 0)
            
            total_weight += weight
            weighted_probability += stats.get('success_probability', 0) * weight
            weighted_return += stats.get('average_return', 0) * weight
            weighted_time += stats.get('average_time_to_target', 0) * weight
    
    if total_weight == 0:
        return {
            'probability': 0,
            'expected_return': 0,
            'expected_time': 0,
            'confidence_score': 0,
            'risk_level': 'عالي'
        }
    
    probability = weighted_probability / total_weight
    expected_return = weighted_return / total_weight
    expected_time = weighted_time / total_weight
    
    confidence_score = min(100, (probability * 0.4 + (expected_return / 10) * 0.3 + 
                               (len(active_indicators) / 9) * 0.3) * 100)
    
    if confidence_score >= 80:
        risk_level = 'منخفض'
    elif confidence_score >= 60:
        risk_level = 'متوسط'
    else:
        risk_level = 'عالي'
    
    return {
        'probability': probability,
        'expected_return': expected_return,
        'expected_time': expected_time * 4,  # تحويل إلى ساعات
        'confidence_score': confidence_score,
        'risk_level': risk_level,
        'active_indicators_count': len(active_indicators)
    }

def analyze_symbol(symbol):
    """تحليل عملة واحدة"""
    exchange = ccxt.binance()
    
    try:
        ohlcv = exchange.fetch_ohlcv(symbol, TIMEFRAME, limit=LIMIT)
        if not ohlcv or len(ohlcv) < 50:
            logger.warning(f"⚠️ بيانات غير كافية لـ {symbol}")
            return
        
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        df = calculate_indicators(df)
        if df is None:
            return
        
        signals, active_signals = detect_signals(df)
        if signals is None:
            return
        
        metrics = calculate_prediction_metrics(signals)
        
        if (metrics['probability'] >= MIN_PROBABILITY_THRESHOLD and 
            metrics['expected_return'] >= MIN_EXPECTED_RETURN):
            
            current_price = df['close'].iloc[-1]
            predicted_price = current_price * (1 + metrics['expected_return'] / 100)
            
            message = f"🚨 *إشارة صاعدة جديدة*\n\n"
            message += f"💰 *العملة:* {symbol}\n"
            message += f"💵 *السعر الحالي:* ${current_price:.6f}\n"
            message += f"🔮 *السعر المتوقع (48س):* ${predicted_price:.6f}\n\n"
            message += f"🎯 *احتمالية الصعود:* {metrics['probability']:.1%}\n"
            message += f"📊 *العائد المتوقع:* {metrics['expected_return']:+.2f}%\n"
            message += f"⏰ *المدة المتوقعة:* {metrics['expected_time']:.1f} ساعة\n"
            message += f"🔒 *نقاط الثقة:* {metrics['confidence_score']:.0f}/100\n"
            message += f"⚠️ *مستوى المخاطر:* {metrics['risk_level']}\n\n"
            
            if active_signals:
                message += f"✅ *الإشارات النشطة* ({metrics['active_indicators_count']}/9):\n"
                for signal in active_signals:
                    message += f"• {signal}\n"
            
            message += f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            logger.info(f"📤 إرسال تنبيه لـ {symbol}")
            send_telegram(message)
            
    except Exception as e:
        logger.error(f"❌ خطأ في تحليل {symbol}: {e}")

def main():
    """الوظيفة الرئيسية"""
    logger.info("🚀 بدء تشغيل نظام مراقبة العملات")
    
    if not load_advanced_weights():
        logger.error("❌ فشل في تحميل الأوزان")
        return
    
    symbols = load_symbols()
    if not symbols:
        logger.error("❌ لا توجد عملات لمراقبتها")
        return
    
    logger.info(f"📋 تم تحميل {len(symbols)} عملة للمراقبة")
    
    try:
        while True:
            logger.info("🔄 بدء دورة جديدة")
            
            for symbol in symbols:
                analyze_symbol(symbol)
                time.sleep(2)
            
            logger.info("✅ انتهت الدورة - انتظار 15 دقيقة")
            time.sleep(900)
            
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف النظام")
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    main()
