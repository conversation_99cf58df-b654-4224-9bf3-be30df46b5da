import ccxt
import pandas as pd
import ta
import requests
import time
import os
import json
import numpy as np
from datetime import datetime, timedelta
import logging

# === إعداد السجلات ===
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crypto_monitor_advanced.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# === إعداد تليجرام ===
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN", "**********************************************")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID", "766367805")

# === إعدادات النظام ===
TIMEFRAME = '4h'
LIMIT = 100
ADVANCED_WEIGHTS_FILE = 'advanced_weights.json'
MIN_PROBABILITY_THRESHOLD = 0.65  # 65% احتمالية نجاح كحد أدنى
MIN_EXPECTED_RETURN = 3.0  # 3% عائد متوقع كحد أدنى
COOLDOWN_HOURS = 12

# === متغيرات عامة ===
last_alerts = {}
advanced_weights = {}

class PredictionEngine:
    """محرك التنبؤ المتقدم"""
    
    def __init__(self):
        self.load_advanced_weights()
    
    def load_advanced_weights(self):
        """تحميل الأوزان والإحصائيات المتقدمة"""
        global advanced_weights
        try:
            with open(ADVANCED_WEIGHTS_FILE, 'r', encoding='utf-8') as f:
                advanced_weights = json.load(f)
            logger.info(f"✅ تم تحميل الإحصائيات المتقدمة من {ADVANCED_WEIGHTS_FILE}")
            return True
        except FileNotFoundError:
            logger.error(f"❌ لم يتم العثور على ملف {ADVANCED_WEIGHTS_FILE}")
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الإحصائيات: {e}")
            return False
    
    def calculate_prediction_metrics(self, signals):
        """حساب مقاييس التنبؤ المتقدمة"""
        if not signals or not advanced_weights:
            return {
                'probability': 0,
                'expected_return': 0,
                'expected_time': 0,
                'confidence_score': 0,
                'risk_level': 'عالي'
            }
        
        # جمع إحصائيات المؤشرات النشطة
        active_indicators = [indicator for indicator, is_active in signals.items() if is_active]
        
        if not active_indicators:
            return {
                'probability': 0,
                'expected_return': 0,
                'expected_time': 0,
                'confidence_score': 0,
                'risk_level': 'عالي'
            }
        
        # حساب المتوسط المرجح للاحتمالية
        total_weight = 0
        weighted_probability = 0
        weighted_return = 0
        weighted_time = 0
        weighted_sharpe = 0
        
        for indicator in active_indicators:
            if indicator in advanced_weights:
                stats = advanced_weights[indicator]
                weight = stats.get('weight', 0)
                
                total_weight += weight
                weighted_probability += stats.get('success_probability', 0) * weight
                weighted_return += stats.get('average_return', 0) * weight
                weighted_time += stats.get('average_time_to_target', 0) * weight
                weighted_sharpe += stats.get('sharpe_ratio', 0) * weight
        
        if total_weight == 0:
            return {
                'probability': 0,
                'expected_return': 0,
                'expected_time': 0,
                'confidence_score': 0,
                'risk_level': 'عالي'
            }
        
        # حساب المتوسطات المرجحة
        probability = weighted_probability / total_weight
        expected_return = weighted_return / total_weight
        expected_time = weighted_time / total_weight
        avg_sharpe = weighted_sharpe / total_weight
        
        # حساب نقاط الثقة (0-100)
        confidence_score = min(100, (probability * 0.4 + (expected_return / 10) * 0.3 + 
                                   (len(active_indicators) / 9) * 0.2 + 
                                   min(avg_sharpe / 2, 1) * 0.1) * 100)
        
        # تحديد مستوى المخاطر
        if confidence_score >= 80:
            risk_level = 'منخفض'
        elif confidence_score >= 60:
            risk_level = 'متوسط'
        else:
            risk_level = 'عالي'
        
        return {
            'probability': probability,
            'expected_return': expected_return,
            'expected_time': expected_time * 4,  # تحويل إلى ساعات (كل فترة = 4 ساعات)
            'confidence_score': confidence_score,
            'risk_level': risk_level,
            'active_indicators_count': len(active_indicators),
            'total_weight': total_weight
        }

def send_telegram(message):
    """إرسال رسالة تليجرام"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    data = {'chat_id': CHAT_ID, 'text': message, 'parse_mode': 'Markdown'}
    try:
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("✅ تم إرسال التنبيه بنجاح")
        else:
            logger.error(f"❌ فشل إرسال التنبيه: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ خطأ في إرسال التنبيه: {e}")

def load_symbols(filename="symbols.txt"):
    """تحميل قائمة العملات"""
    if not os.path.exists(filename):
        logger.error(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                symbol = line.replace("/", "")
                symbols.append(symbol)
        return symbols

def can_send_alert(symbol):
    """فحص إمكانية إرسال تنبيه للعملة"""
    if symbol not in last_alerts:
        return True
    
    last_alert_time = last_alerts[symbol]
    time_diff = datetime.now() - last_alert_time
    return time_diff.total_seconds() > (COOLDOWN_HOURS * 3600)

def calculate_advanced_indicators(df):
    """حساب المؤشرات المتقدمة"""
    try:
        # RSI
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
        
        # MACD
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        
        # EMA
        df['ema20'] = ta.trend.EMAIndicator(close=df['close'], window=20).ema_indicator()
        df['ema50'] = ta.trend.EMAIndicator(close=df['close'], window=50).ema_indicator()
        
        # ADX
        adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        df['adx'] = adx.adx()
        
        # Bollinger Bands
        bb = ta.volatility.BollingerBands(close=df['close'])
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_lower'] = bb.bollinger_lband()
        
        # OBV
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
        
        # CMF
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'], low=df['low'], close=df['close'], volume=df['volume']
        ).chaikin_money_flow()
        
        # Stochastic RSI
        stoch_rsi = ta.momentum.StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
        df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
        df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
        
        # Volume Spike
        df['volume_avg_20'] = df['volume'].rolling(window=20).mean()
        df['volume_spike_ratio'] = df['volume'] / df['volume_avg_20']
        
        return df
    except Exception as e:
        logger.error(f"❌ خطأ في حساب المؤشرات: {e}")
        return None

def detect_bullish_signals(df):
    """كشف الإشارات الصاعدة"""
    if df is None or len(df) < 50:
        return None, []
    
    signals = {}
    active_signals = []
    
    try:
        # RSI Bullish
        rsi_val = df['rsi'].iloc[-1]
        signals['rsi_bullish'] = 30 <= rsi_val <= 70
        if signals['rsi_bullish']:
            active_signals.append(f"RSI: {rsi_val:.1f}")
        
        # MACD Bullish
        macd_val = df['macd'].iloc[-1]
        macd_signal_val = df['macd_signal'].iloc[-1]
        signals['macd_bullish'] = macd_val > macd_signal_val
        if signals['macd_bullish']:
            active_signals.append("MACD إيجابي")
        
        # ADX Strong Trend
        adx_val = df['adx'].iloc[-1]
        signals['adx_bullish'] = adx_val > 25
        if signals['adx_bullish']:
            active_signals.append(f"ADX: {adx_val:.1f}")
        
        # Bollinger Breakout
        close_val = df['close'].iloc[-1]
        bb_upper_val = df['bb_upper'].iloc[-1]
        signals['bollinger_breakout'] = close_val > bb_upper_val
        if signals['bollinger_breakout']:
            active_signals.append("اختراق بولينجر")
        
        # EMA Crossover
        ema20_val = df['ema20'].iloc[-1]
        ema50_val = df['ema50'].iloc[-1]
        signals['ema10_above_ema50'] = ema20_val > ema50_val  # استخدام نفس الاسم للتوافق
        if signals['ema10_above_ema50']:
            active_signals.append("EMA20 > EMA50")
        
        # Volume Spike
        volume_ratio = df['volume_spike_ratio'].iloc[-1]
        signals['volume_spike'] = volume_ratio > 1.5
        if signals['volume_spike']:
            active_signals.append(f"حجم مرتفع: {volume_ratio:.1f}x")
        
        # OBV Trend
        obv_current = df['obv'].iloc[-1]
        obv_prev = df['obv'].iloc[-5] if len(df) >= 5 else df['obv'].iloc[0]
        signals['obv_bullish'] = obv_current > obv_prev
        if signals['obv_bullish']:
            active_signals.append("OBV صاعد")
        
        # CMF Positive
        cmf_val = df['cmf'].iloc[-1]
        signals['cmf_bullish'] = cmf_val > 0
        if signals['cmf_bullish']:
            active_signals.append(f"CMF: {cmf_val:.3f}")
        
        # Stochastic RSI
        stoch_k = df['stoch_rsi_k'].iloc[-1]
        stoch_d = df['stoch_rsi_d'].iloc[-1]
        signals['stoch_rsi_bullish'] = stoch_k > stoch_d and stoch_k < 0.8
        if signals['stoch_rsi_bullish']:
            active_signals.append("StochRSI إيجابي")
        
        return signals, active_signals

    except Exception as e:
        logger.error(f"❌ خطأ في كشف الإشارات: {e}")
        return None, []

def format_time_duration(hours):
    """تنسيق المدة الزمنية"""
    if hours < 1:
        return f"{int(hours * 60)} دقيقة"
    elif hours < 24:
        return f"{hours:.1f} ساعة"
    else:
        days = hours / 24
        return f"{days:.1f} يوم"

def analyze_symbol(symbol, prediction_engine):
    """تحليل عملة واحدة مع التنبؤات المتقدمة"""
    exchange = ccxt.binance()

    try:
        # تحميل البيانات
        ohlcv = exchange.fetch_ohlcv(symbol, TIMEFRAME, limit=LIMIT)
        if not ohlcv or len(ohlcv) < 50:
            logger.warning(f"⚠️ بيانات غير كافية لـ {symbol}")
            return

        # تحويل إلى DataFrame
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)

        # حساب المؤشرات
        df = calculate_advanced_indicators(df)
        if df is None:
            return

        # كشف الإشارات
        signals, active_signals = detect_bullish_signals(df)
        if signals is None:
            return

        # حساب مقاييس التنبؤ المتقدمة
        prediction_metrics = prediction_engine.calculate_prediction_metrics(signals)

        # فحص العتبات
        probability = prediction_metrics['probability']
        expected_return = prediction_metrics['expected_return']

        if (probability >= MIN_PROBABILITY_THRESHOLD and
            expected_return >= MIN_EXPECTED_RETURN and
            can_send_alert(symbol)):

            # حساب معلومات إضافية
            current_price = df['close'].iloc[-1]
            price_change_24h = ((current_price - df['close'].iloc[-6]) / df['close'].iloc[-6]) * 100

            # إنشاء الرسالة المتقدمة
            message = f"🚨 *إشارة صاعدة متقدمة*\n\n"
            message += f"💰 *العملة:* {symbol}\n"
            message += f"💵 *السعر الحالي:* ${current_price:.6f}\n"
            message += f"📈 *التغيير 24س:* {price_change_24h:+.2f}%\n\n"

            # مقاييس التنبؤ
            message += f"🎯 *احتمالية الصعود:* {probability:.1%}\n"
            message += f"📊 *العائد المتوقع:* {expected_return:+.2f}%\n"
            message += f"⏰ *المدة المتوقعة:* {format_time_duration(prediction_metrics['expected_time'])}\n"
            message += f"🔒 *نقاط الثقة:* {prediction_metrics['confidence_score']:.0f}/100\n"
            message += f"⚠️ *مستوى المخاطر:* {prediction_metrics['risk_level']}\n\n"

            # الإشارات النشطة
            message += f"✅ *الإشارات النشطة* ({prediction_metrics['active_indicators_count']}/9):\n"
            for signal in active_signals:
                message += f"• {signal}\n"

            # توصيات التداول
            message += f"\n💡 *توصيات التداول:*\n"
            if prediction_metrics['risk_level'] == 'منخفض':
                message += f"• حجم المركز المقترح: 3-5%\n"
                message += f"• وقف الخسارة: {current_price * 0.95:.6f} (-5%)\n"
                message += f"• هدف الربح: {current_price * (1 + expected_return/100):.6f} (+{expected_return:.1f}%)\n"
            elif prediction_metrics['risk_level'] == 'متوسط':
                message += f"• حجم المركز المقترح: 2-3%\n"
                message += f"• وقف الخسارة: {current_price * 0.97:.6f} (-3%)\n"
                message += f"• هدف الربح: {current_price * (1 + expected_return/100):.6f} (+{expected_return:.1f}%)\n"
            else:
                message += f"• حجم المركز المقترح: 1-2%\n"
                message += f"• وقف الخسارة: {current_price * 0.98:.6f} (-2%)\n"
                message += f"• هدف الربح: {current_price * (1 + expected_return/100):.6f} (+{expected_return:.1f}%)\n"

            message += f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # إرسال التنبيه
            logger.info(f"📤 إرسال تنبيه متقدم لـ {symbol}")
            logger.info(f"   احتمالية: {probability:.1%}, عائد متوقع: {expected_return:+.2f}%")
            send_telegram(message)

            # تسجيل وقت التنبيه
            last_alerts[symbol] = datetime.now()

        elif probability >= 0.4 or expected_return >= 2.0:  # تسجيل الإشارات الضعيفة
            logger.info(f"📊 {symbol}: احتمالية {probability:.1%}, عائد متوقع {expected_return:+.2f}% - تحت العتبة")

    except Exception as e:
        logger.error(f"❌ خطأ في تحليل {symbol}: {e}")

def cleanup_old_alerts():
    """تنظيف التنبيهات القديمة"""
    current_time = datetime.now()
    to_remove = []

    for symbol, alert_time in last_alerts.items():
        if (current_time - alert_time).total_seconds() > (COOLDOWN_HOURS * 3600):
            to_remove.append(symbol)

    for symbol in to_remove:
        del last_alerts[symbol]

    if to_remove:
        logger.info(f"🧹 تم تنظيف {len(to_remove)} تنبيه قديم")

def main():
    """الوظيفة الرئيسية"""
    logger.info("🚀 بدء تشغيل نظام مراقبة العملات المتقدم...")

    # تهيئة محرك التنبؤ
    prediction_engine = PredictionEngine()
    if not advanced_weights:
        logger.error("❌ فشل في تحميل الإحصائيات المتقدمة. تأكد من تشغيل statistics - New.py أولاً")
        return

    # تحميل العملات
    symbols = load_symbols()
    if not symbols:
        logger.error("❌ لا توجد عملات لمراقبتها")
        return

    logger.info(f"📋 تم تحميل {len(symbols)} عملة للمراقبة")
    logger.info(f"⚙️ الإعدادات:")
    logger.info(f"   إطار زمني: {TIMEFRAME}")
    logger.info(f"   احتمالية نجاح دنيا: {MIN_PROBABILITY_THRESHOLD:.1%}")
    logger.info(f"   عائد متوقع أدنى: {MIN_EXPECTED_RETURN}%")

    cycle_count = 0

    try:
        while True:
            cycle_count += 1
            start_time = time.time()

            logger.info(f"🔄 بدء الدورة #{cycle_count}")

            analyzed_count = 0

            for i, symbol in enumerate(symbols):
                try:
                    analyze_symbol(symbol, prediction_engine)
                    analyzed_count += 1

                    # فترة انتظار بين العملات
                    time.sleep(2)

                    # عرض التقدم
                    if (i + 1) % 20 == 0:
                        logger.info(f"📊 تم تحليل {i + 1}/{len(symbols)} عملة")

                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة {symbol}: {e}")
                    continue

            # تنظيف التنبيهات القديمة
            cleanup_old_alerts()

            cycle_time = time.time() - start_time
            logger.info(f"✅ انتهت الدورة #{cycle_count} - تم تحليل {analyzed_count} عملة في {cycle_time:.1f} ثانية")

            # انتظار قبل الدورة التالية
            wait_time = 900  # 15 دقيقة
            logger.info(f"⏳ انتظار {wait_time//60} دقيقة قبل الدورة التالية...")
            time.sleep(wait_time)

    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ عام في النظام: {e}")

if __name__ == "__main__":
    main()
