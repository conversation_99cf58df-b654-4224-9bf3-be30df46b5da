#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف الإعدادات للنظام المتكامل
"""

# إعدادات التليجرام
TELEGRAM_CONFIG = {
    "bot_token": "**********************************************",  # التوكن الصحيح
    "chat_id": "766367805"  # ضع Chat ID الرقمي الصحيح هنا (مثال: 1234567890)
}

# إعدادات النموذج
MODEL_CONFIG = {
    "model_path": "simple_crypto_model.h5",
    "scaler_path": "simple_crypto_model_scaler.pkl",
    "sequence_length": 18,
    "min_confidence": 0.70,
    "target_return": 0.08,
    "tracking_hours": 72
}

# إعدادات التحليل
ANALYSIS_CONFIG = {
    "max_workers": 20,  # زيادة عدد الخيوط للسرعة
    "max_cryptos": -1,  # -1 يعني جميع العملات المحفوظة (لا حدود)
    "timeframe": "4h",
    "data_limit": 100
}

# إعدادات الملفات
FILES_CONFIG = {
    "recommendations_history": "recommendations_history.json",
    "performance_stats": "performance_stats.json",
    "logs_dir": "logs"
}

# العملات المستبعدة
EXCLUDED_COINS = [
    'USDC', 'BUSD', 'DAI', 'TUSD', 'USDD', 'FDUSD', 'USDT'
]

# العملات الاحتياطية
FALLBACK_CRYPTOS = [
    'BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'DOGE', 'SOL', 'TRX', 'DOT', 'MATIC',
    'SHIB', 'AVAX', 'UNI', 'LINK', 'ATOM', 'XMR', 'ETC', 'BCH', 'LTC', 'FIL',
    'APT', 'NEAR', 'VET', 'ICP', 'GRT', 'SAND', 'MANA', 'ALGO', 'FLOW', 'XTZ'
]
