#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام جمع البيانات متعدد الأطر الزمنية
الهدف: جمع بيانات من 1h, 4h, 1d لتحليل أكثر دقة
"""

import ccxt
import pandas as pd
import os
import ta
from datetime import datetime
import time

# إعدادات متعددة الأطر الزمنية
TIMEFRAMES = {
    '1h': {'limit': 720, 'name': '1hour'},    # 30 يوم
    '4h': {'limit': 360, 'name': '4hour'},    # 60 يوم  
    '1d': {'limit': 180, 'name': '1day'}      # 180 يوم
}

DATA_DIR = 'multi_timeframe_data'

def load_symbols(filename="symbols.txt"):
    """تحميل قائمة العملات"""
    if not os.path.exists(filename):
        print(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                symbols.append(line)
        return symbols

def fetch_ohlcv_timeframe(symbol, timeframe, limit):
    """جلب بيانات OHLCV لإطار زمني محدد"""
    exchange = ccxt.binance()
    try:
        print(f"⬇️ تحميل {symbol} ({timeframe}, {limit} شمعة)...")
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
        
        if not ohlcv or len(ohlcv) < 50:
            print(f"⚠️ بيانات غير كافية لـ {symbol} في {timeframe}")
            return None
            
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        return df
        
    except Exception as e:
        print(f"❌ خطأ في جلب بيانات {symbol} ({timeframe}): {e}")
        return None

def calculate_advanced_indicators(df, timeframe):
    """حساب مؤشرات متقدمة حسب الإطار الزمني"""
    try:
        # المؤشرات الأساسية
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close'], window=14).rsi()
        
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        df['macd_histogram'] = macd.macd_diff()
        
        # ADX مع مكوناته
        adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'], window=14)
        df['adx'] = adx.adx()
        df['adx_pos'] = adx.adx_pos()
        df['adx_neg'] = adx.adx_neg()
        
        # مؤشرات الحجم
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'], low=df['low'], close=df['close'], volume=df['volume'], window=20
        ).chaikin_money_flow()
        
        # مؤشرات متقدمة حسب الإطار الزمني
        if timeframe == '1h':
            # للساعة: مؤشرات سريعة
            df['rsi_fast'] = ta.momentum.RSIIndicator(close=df['close'], window=7).rsi()
            df['ema_fast'] = ta.trend.EMAIndicator(close=df['close'], window=12).ema_indicator()
            df['ema_slow'] = ta.trend.EMAIndicator(close=df['close'], window=26).ema_indicator()
            
        elif timeframe == '4h':
            # لـ 4 ساعات: مؤشرات متوسطة
            df['bb_upper'] = ta.volatility.BollingerBands(close=df['close'], window=20).bollinger_hband()
            df['bb_lower'] = ta.volatility.BollingerBands(close=df['close'], window=20).bollinger_lband()
            df['bb_middle'] = ta.volatility.BollingerBands(close=df['close'], window=20).bollinger_mavg()
            
        elif timeframe == '1d':
            # لليوم: مؤشرات طويلة المدى
            df['sma_50'] = ta.trend.SMAIndicator(close=df['close'], window=50).sma_indicator()
            df['sma_200'] = ta.trend.SMAIndicator(close=df['close'], window=200).sma_indicator()
            df['atr'] = ta.volatility.AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()
        
        # مؤشرات مشتركة لجميع الأطر
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['price_change'] = df['close'].pct_change()
        df['volatility'] = df['price_change'].rolling(window=20).std()
        
        return df
        
    except Exception as e:
        print(f"❌ خطأ في حساب المؤشرات: {e}")
        return None

def analyze_big_moves_multi_timeframe(df_1h, df_4h, df_1d, symbol):
    """تحليل الحركات الكبيرة عبر الأطر الزمنية المختلفة"""
    big_moves_found = False
    
    # تحليل كل إطار زمني
    timeframes_data = {
        '1h': df_1h,
        '4h': df_4h, 
        '1d': df_1d
    }
    
    move_thresholds = {
        '1h': 0.05,   # 5% في الساعة
        '4h': 0.08,   # 8% في 4 ساعات
        '1d': 0.12    # 12% في اليوم
    }
    
    for tf, df in timeframes_data.items():
        if df is None or len(df) < 50:
            continue
            
        threshold = move_thresholds[tf]
        moves_count = 0
        
        # البحث عن حركات كبيرة
        for i in range(30, len(df) - 12):
            current_price = df['close'].iloc[i]
            future_prices = df['close'].iloc[i+1:i+13]
            
            if len(future_prices) == 0:
                continue
                
            max_future_price = future_prices.max()
            price_change = (max_future_price - current_price) / current_price
            
            if price_change >= threshold:
                moves_count += 1
        
        if moves_count >= 5:  # على الأقل 5 حركات كبيرة
            big_moves_found = True
            print(f"🎯 {symbol} ({tf}): وجد {moves_count} حركة كبيرة (≥{threshold:.0%})")
    
    return big_moves_found

def process_symbol_multi_timeframe(symbol):
    """معالجة عملة واحدة عبر جميع الأطر الزمنية"""
    symbol_data = {}
    
    # جمع البيانات لجميع الأطر الزمنية
    for timeframe, config in TIMEFRAMES.items():
        df = fetch_ohlcv_timeframe(symbol, timeframe, config['limit'])
        if df is not None:
            df = calculate_advanced_indicators(df, timeframe)
            symbol_data[timeframe] = df
        else:
            symbol_data[timeframe] = None
        
        time.sleep(0.5)  # تجنب حدود API
    
    # تحليل الحركات الكبيرة
    has_big_moves = analyze_big_moves_multi_timeframe(
        symbol_data.get('1h'), 
        symbol_data.get('4h'), 
        symbol_data.get('1d'), 
        symbol
    )
    
    if has_big_moves:
        # حفظ البيانات
        file_symbol = symbol.replace("/", "")
        
        for timeframe, df in symbol_data.items():
            if df is not None:
                timeframe_dir = os.path.join(DATA_DIR, timeframe)
                os.makedirs(timeframe_dir, exist_ok=True)
                
                output_path = os.path.join(timeframe_dir, f"{file_symbol}.csv")
                df.to_csv(output_path)
                print(f"✅ حفظ {symbol} ({timeframe}): {output_path}")
        
        return True
    else:
        print(f"⚠️ {symbol}: لا توجد حركات كبيرة كافية - تم تجاهله")
        return False

def clean_old_data():
    """حذف البيانات القديمة"""
    if os.path.exists(DATA_DIR):
        import shutil
        shutil.rmtree(DATA_DIR)
        print("🗑️ تم حذف البيانات القديمة")
    
    os.makedirs(DATA_DIR, exist_ok=True)
    for timeframe in TIMEFRAMES.keys():
        os.makedirs(os.path.join(DATA_DIR, timeframe), exist_ok=True)

def main():
    print("🚀 نظام جمع البيانات متعدد الأطر الزمنية")
    print("📊 الأطر الزمنية: 1h, 4h, 1d")
    print("🎯 الهدف: تحليل أكثر دقة عبر أطر زمنية متعددة")
    
    # حذف البيانات القديمة
    clean_old_data()
    
    # تحميل قائمة العملات
    symbols = load_symbols()
    if not symbols:
        print("❌ لا توجد عملات للمعالجة")
        return
    
    print(f"📋 سيتم معالجة {len(symbols)} عملة عبر {len(TIMEFRAMES)} أطر زمنية")
    
    processed_count = 0
    saved_count = 0
    
    for i, symbol in enumerate(symbols, 1):
        print(f"\n[{i}/{len(symbols)}] معالجة {symbol}")
        
        try:
            if process_symbol_multi_timeframe(symbol):
                saved_count += 1
            processed_count += 1
            
            if processed_count % 10 == 0:
                print(f"\n📈 تقدم: {processed_count}/{len(symbols)} معالج, {saved_count} محفوظ")
                
        except Exception as e:
            print(f"❌ خطأ في معالجة {symbol}: {e}")
            continue
    
    print(f"\n✅ انتهت المعالجة:")
    print(f"   العملات المعالجة: {processed_count}/{len(symbols)}")
    print(f"   العملات المحفوظة: {saved_count}")
    print(f"   معدل النجاح: {saved_count/processed_count:.1%}")
    
    # إحصائيات الملفات المحفوظة
    for timeframe in TIMEFRAMES.keys():
        timeframe_dir = os.path.join(DATA_DIR, timeframe)
        if os.path.exists(timeframe_dir):
            files_count = len([f for f in os.listdir(timeframe_dir) if f.endswith('.csv')])
            print(f"   {timeframe}: {files_count} ملف")

if __name__ == "__main__":
    main()
