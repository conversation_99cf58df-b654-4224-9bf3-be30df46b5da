#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النظام المتكامل للعملات المشفرة - النسخة النهائية
يجمع بين النموذج المتقدم + التليجرام + تتبع الأداء
"""

import os
import json
import pandas as pd
import numpy as np
import tensorflow as tf
import joblib
import requests
import warnings
from datetime import datetime, timedelta
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from config import *
warnings.filterwarnings('ignore')

class IntegratedCryptoSystem:
    """النظام المتكامل للعملات المشفرة"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.model_loaded = False

        # تحميل الإعدادات من ملف config
        self.sequence_length = MODEL_CONFIG["sequence_length"]
        self.min_confidence = MODEL_CONFIG["min_confidence"]
        self.target_return = MODEL_CONFIG["target_return"]
        self.tracking_hours = MODEL_CONFIG["tracking_hours"]

        # إعدادات التليجرام
        self.bot_token = TELEGRAM_CONFIG["bot_token"]
        self.chat_id = TELEGRAM_CONFIG["chat_id"]

        # ملفات التتبع
        self.recommendations_file = FILES_CONFIG["recommendations_history"]
        self.performance_file = FILES_CONFIG["performance_stats"]
        
        print("🚀 النظام المتكامل للعملات المشفرة")
        print("🧠 نموذج متقدم + تليجرام + تتبع الأداء")
        
    def load_model(self):
        """تحميل النموذج والمعايرات"""
        try:
            print("📥 تحميل النموذج المدرب...")
            self.model = tf.keras.models.load_model(MODEL_CONFIG["model_path"])
            self.scaler = joblib.load(MODEL_CONFIG["scaler_path"])
            self.model_loaded = True
            print("✅ تم تحميل النموذج بنجاح!")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج: {e}")
            return False
    
    def load_recommendations_history(self):
        """تحميل تاريخ التوصيات"""
        try:
            if os.path.exists(self.recommendations_file):
                with open(self.recommendations_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"❌ خطأ في تحميل تاريخ التوصيات: {e}")
            return []
    
    def save_recommendations_history(self, history):
        """حفظ تاريخ التوصيات"""
        try:
            with open(self.recommendations_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطأ في حفظ تاريخ التوصيات: {e}")
    
    def load_performance_stats(self):
        """تحميل إحصائيات الأداء"""
        try:
            if os.path.exists(self.performance_file):
                with open(self.performance_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                "total_recommendations": 0,
                "successful_recommendations": 0,
                "success_rate": 0.0,
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            print(f"❌ خطأ في تحميل إحصائيات الأداء: {e}")
            return {"total_recommendations": 0, "successful_recommendations": 0, "success_rate": 0.0}
    
    def save_performance_stats(self, stats):
        """حفظ إحصائيات الأداء"""
        try:
            stats["last_updated"] = datetime.now().isoformat()
            with open(self.performance_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ خطأ في حفظ إحصائيات الأداء: {e}")
    
    def get_crypto_data_from_file(self, symbol, timeframe='4h'):
        """جلب بيانات العملة من الملفات المحفوظة"""
        try:
            file_path = f"multi_timeframe_data/{timeframe}/{symbol}USDT.csv"

            if not os.path.exists(file_path):
                return None

            df = pd.read_csv(file_path, index_col=0, parse_dates=True)

            # التأكد من وجود الأعمدة المطلوبة
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_cols):
                return None

            # أخذ آخر 100 صف فقط للسرعة
            return df[required_cols].tail(100)

        except Exception as e:
            return None

    def get_crypto_data_from_api(self, symbol, timeframe='4h', limit=100):
        """جلب بيانات العملة من Binance API"""
        try:
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': f"{symbol}USDT",
                'interval': timeframe,
                'limit': limit
            }

            response = requests.get(url, params=params, timeout=10)
            if response.status_code != 200:
                return None

            data = response.json()

            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])

            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])

            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            return df[['open', 'high', 'low', 'close', 'volume']]

        except Exception as e:
            return None

    def get_crypto_data(self, symbol, timeframe='4h', limit=100):
        """جلب بيانات العملة - يحاول الملفات المحفوظة أولاً ثم API"""
        # محاولة جلب البيانات من الملفات المحفوظة أولاً (أسرع)
        df = self.get_crypto_data_from_file(symbol, timeframe)

        if df is not None and len(df) >= 50:  # إذا كانت البيانات كافية
            return df

        # إذا فشلت أو كانت البيانات قليلة، استخدم API
        return self.get_crypto_data_from_api(symbol, timeframe, limit)
    
    def calculate_indicators(self, df):
        """حساب المؤشرات الفنية"""
        try:
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            df['macd'] = exp1 - exp2
            
            # OBV
            df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
            
            # CMF
            mfm = ((df['close'] - df['low']) - (df['high'] - df['close'])) / (df['high'] - df['low'])
            mfm = mfm.fillna(0)
            mfv = mfm * df['volume']
            df['cmf'] = mfv.rolling(window=20).sum() / df['volume'].rolling(window=20).sum()
            
            return df
            
        except Exception as e:
            return None
    
    def prepare_sequence(self, df):
        """تحضير التسلسل للتنبؤ"""
        try:
            features = ['close', 'volume', 'rsi', 'macd', 'obv', 'cmf']
            available_features = [f for f in features if f in df.columns]
            
            if len(available_features) < 4:
                return None
            
            df_clean = df[available_features].dropna()
            
            if len(df_clean) < self.sequence_length:
                return None
            
            scaled_data = self.scaler.transform(df_clean)
            sequence = scaled_data[-self.sequence_length:].reshape(1, self.sequence_length, -1)
            
            return sequence
            
        except Exception as e:
            return None
    
    def predict_crypto(self, symbol):
        """التنبؤ لعملة واحدة"""
        try:
            df = self.get_crypto_data(symbol)
            if df is None:
                return None
            
            df = self.calculate_indicators(df)
            if df is None:
                return None
            
            sequence = self.prepare_sequence(df)
            if sequence is None:
                return None
            
            prediction = self.model.predict(sequence, verbose=0)[0][0]
            
            current_price = df['close'].iloc[-1]
            volume_24h = df['volume'].iloc[-24:].sum() if len(df) >= 24 else df['volume'].sum()
            price_change_24h = ((current_price - df['close'].iloc[-25]) / df['close'].iloc[-25] * 100) if len(df) >= 25 else 0
            
            return {
                'symbol': symbol,
                'probability': float(prediction),
                'confidence': 'عالية' if prediction > 0.8 else 'متوسطة' if prediction > 0.6 else 'منخفضة',
                'signal': bool(prediction > self.min_confidence),
                'current_price': float(current_price),
                'volume_24h': float(volume_24h),
                'price_change_24h': float(price_change_24h),
                'target_price': float(current_price * (1 + self.target_return)),
                'timestamp': datetime.now().isoformat(),
                'expiry_time': (datetime.now() + timedelta(hours=self.tracking_hours)).isoformat()
            }
            
        except Exception as e:
            return None
    
    def get_all_saved_cryptos(self):
        """الحصول على جميع العملات المحفوظة في قاعدة البيانات"""
        try:
            data_dir = "multi_timeframe_data/4h"
            if not os.path.exists(data_dir):
                print(f"❌ مجلد البيانات غير موجود: {data_dir}")
                return self.get_fallback_cryptos()

            # جلب جميع ملفات CSV
            csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

            # استخراج أسماء العملات من أسماء الملفات
            cryptos = []
            for filename in csv_files:
                # إزالة USDT.csv للحصول على اسم العملة
                crypto = filename.replace('USDT.csv', '')
                if crypto not in EXCLUDED_COINS:
                    cryptos.append(crypto)

            print(f"📊 تم العثور على {len(cryptos)} عملة محفوظة")
            return cryptos

        except Exception as e:
            print(f"❌ خطأ في جلب العملات المحفوظة: {e}")
            return self.get_fallback_cryptos()

    def get_top_cryptos(self):
        """الحصول على العملات للتحليل - يستخدم العملات المحفوظة أولاً"""
        # استخدام العملات المحفوظة أولاً
        saved_cryptos = self.get_all_saved_cryptos()

        if len(saved_cryptos) > 50:  # إذا كان لدينا عملات محفوظة كافية
            print(f"✅ استخدام العملات المحفوظة: {len(saved_cryptos)} عملة")
            return saved_cryptos
        else:
            # إذا لم تكن كافية، استخدم Binance API كبديل
            print("⚠️ العملات المحفوظة قليلة، استخدام Binance API...")
            return self.get_binance_cryptos()

    def get_binance_cryptos(self):
        """الحصول على أفضل العملات من Binance (احتياطي)"""
        try:
            url = "https://api.binance.com/api/v3/ticker/24hr"
            response = requests.get(url, timeout=10)

            if response.status_code != 200:
                return self.get_fallback_cryptos()

            data = response.json()
            usdt_pairs = [item for item in data if item['symbol'].endswith('USDT')]
            usdt_pairs.sort(key=lambda x: float(x['quoteVolume']), reverse=True)

            top_cryptos = [pair['symbol'].replace('USDT', '') for pair in usdt_pairs[:100]]
            top_cryptos = [crypto for crypto in top_cryptos if crypto not in EXCLUDED_COINS]

            return top_cryptos

        except Exception as e:
            return self.get_fallback_cryptos()
    
    def get_fallback_cryptos(self):
        """قائمة احتياطية للعملات"""
        return FALLBACK_CRYPTOS
    
    def check_expired_recommendations(self):
        """فحص التوصيات المنتهية الصلاحية وتحديث الإحصائيات"""
        print("🔍 فحص التوصيات المنتهية الصلاحية...")
        
        history = self.load_recommendations_history()
        stats = self.load_performance_stats()
        
        updated_history = []
        newly_expired = []
        
        for rec in history:
            if 'checked' not in rec or not rec['checked']:
                expiry_time = datetime.fromisoformat(rec['expiry_time'])
                
                if datetime.now() > expiry_time:
                    # فحص النتيجة
                    current_data = self.get_crypto_data(rec['symbol'], limit=200)
                    if current_data is not None:
                        # البحث عن أعلى سعر خلال فترة التتبع
                        start_time = datetime.fromisoformat(rec['timestamp'])
                        tracking_data = current_data[current_data.index > start_time]
                        
                        if len(tracking_data) > 0:
                            max_price = tracking_data['high'].max()
                            target_reached = max_price >= rec['target_price']
                            
                            rec['checked'] = True
                            rec['max_price_reached'] = float(max_price)
                            rec['target_reached'] = target_reached
                            rec['actual_return'] = float((max_price - rec['current_price']) / rec['current_price'])
                            
                            newly_expired.append(rec)
                            
                            # تحديث الإحصائيات
                            stats['total_recommendations'] += 1
                            if target_reached:
                                stats['successful_recommendations'] += 1
            
            updated_history.append(rec)
        
        # حساب معدل النجاح
        if stats['total_recommendations'] > 0:
            stats['success_rate'] = stats['successful_recommendations'] / stats['total_recommendations']
        
        # حفظ التحديثات
        self.save_recommendations_history(updated_history)
        self.save_performance_stats(stats)
        
        if newly_expired:
            print(f"📊 تم فحص {len(newly_expired)} توصية منتهية الصلاحية")
            
        return stats

    def send_telegram_message(self, message):
        """إرسال رسالة إلى التليجرام"""
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }

            response = requests.post(url, data=data, timeout=10)
            return response.status_code == 200

        except Exception as e:
            print(f"❌ خطأ في إرسال رسالة التليجرام: {e}")
            return False

    def format_recommendation_message(self, rec, stats):
        """تنسيق رسالة التوصية"""
        confidence_emoji = "🔥" if rec['probability'] > 0.8 else "⚡" if rec['probability'] > 0.6 else "💡"

        message = f"""
{confidence_emoji} <b>توصية جديدة من النظام المتقدم</b>

💰 <b>العملة:</b> #{rec['symbol']}
📊 <b>الاحتمالية:</b> {rec['probability']:.1%}
🎯 <b>مستوى الثقة:</b> {rec['confidence']}

💵 <b>السعر الحالي:</b> ${rec['current_price']:.4f}
🎯 <b>السعر المستهدف:</b> ${rec['target_price']:.4f}
📈 <b>الهدف:</b> +{self.target_return:.1%}

📊 <b>التغير 24h:</b> {rec['price_change_24h']:+.2f}%
📊 <b>الحجم 24h:</b> ${rec['volume_24h']:,.0f}

⏰ <b>مدة التتبع:</b> {self.tracking_hours} ساعة
📅 <b>وقت التحليل:</b> {datetime.fromisoformat(rec['timestamp']).strftime('%Y-%m-%d %H:%M')}

📊 <b>إحصائيات النظام:</b>
✅ <b>معدل النجاح:</b> {stats['success_rate']:.1%}
📈 <b>إجمالي التوصيات:</b> {stats['total_recommendations']}
🎯 <b>التوصيات الناجحة:</b> {stats['successful_recommendations']}

🧠 <i>مدرب على 379 عملة و 115K+ عينة | دقة 90%</i>
        """

        return message.strip()

    def analyze_and_send(self, max_workers=10):
        """تحليل السوق وإرسال التوصيات"""
        if not self.model_loaded:
            if not self.load_model():
                return []

        # فحص التوصيات المنتهية أولاً
        stats = self.check_expired_recommendations()

        print("🔍 بدء تحليل السوق...")
        cryptos = self.get_top_cryptos()
        print(f"💰 سيتم تحليل {len(cryptos)} عملة")

        results = []
        history = self.load_recommendations_history()

        # تحليل متوازي
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_crypto = {executor.submit(self.predict_crypto, crypto): crypto for crypto in cryptos}

            for i, future in enumerate(as_completed(future_to_crypto)):
                crypto = future_to_crypto[future]
                try:
                    result = future.result()
                    if result and result['signal']:
                        results.append(result)

                        # إضافة إلى التاريخ
                        history.append(result)

                        # إرسال إلى التليجرام
                        message = self.format_recommendation_message(result, stats)
                        if self.send_telegram_message(message):
                            print(f"✅ تم إرسال توصية {result['symbol']} إلى التليجرام")
                        else:
                            print(f"❌ فشل إرسال توصية {result['symbol']} إلى التليجرام")

                    if (i + 1) % 5 == 0:
                        print(f"📈 تم تحليل {i + 1}/{len(cryptos)} عملة...")

                except Exception as e:
                    print(f"❌ خطأ في تحليل {crypto}: {e}")

        # حفظ التاريخ المحدث
        self.save_recommendations_history(history)

        # ترتيب النتائج
        results.sort(key=lambda x: x['probability'], reverse=True)

        return results

    def display_summary(self, results, stats):
        """عرض ملخص النتائج"""
        print("\n" + "="*80)
        print("📊 ملخص النظام المتكامل")
        print("="*80)

        print(f"🎯 توصيات جديدة: {len(results)}")
        print(f"📊 معدل النجاح الحالي: {stats['success_rate']:.1%}")
        print(f"📈 إجمالي التوصيات: {stats['total_recommendations']}")
        print(f"✅ التوصيات الناجحة: {stats['successful_recommendations']}")

        if results:
            print(f"⚡ متوسط الاحتمالية: {np.mean([r['probability'] for r in results]):.1%}")
            print(f"🔥 أفضل توصية: {results[0]['symbol']} ({results[0]['probability']:.1%})")

        print(f"⏰ آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def run_full_analysis(self):
        """تشغيل التحليل الكامل"""
        print("🚀 بدء النظام المتكامل...")
        print("🧠 نموذج متقدم + تليجرام + تتبع الأداء")

        start_time = time.time()
        results = self.analyze_and_send()
        end_time = time.time()

        stats = self.load_performance_stats()

        print(f"\n⏱️ وقت التحليل: {end_time - start_time:.1f} ثانية")
        self.display_summary(results, stats)

        return results, stats

def main():
    """الوظيفة الرئيسية"""
    system = IntegratedCryptoSystem()
    results, stats = system.run_full_analysis()

    print("\n🎉 تم الانتهاء من التحليل بنجاح!")
    print("📱 تم إرسال التوصيات إلى التليجرام")
    print("📊 تم تحديث إحصائيات الأداء")

if __name__ == "__main__":
    main()
