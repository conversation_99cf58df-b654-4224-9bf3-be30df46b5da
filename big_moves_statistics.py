#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إحصائيات الارتفاعات الكبيرة - التركيز على أرباح 15%+
الهدف: تحليل الإشارات التي سبقت الارتفاعات الكبيرة فقط
لإنتاج توصيات تحقق أرباح أكثر من 10%
"""

import os
import pandas as pd
import json
import numpy as np

# إعدادات
DATA_DIR = "data"
OUTPUT_FILE = "big_moves_weights.json"

# المؤشرات الأساسية
INDICATORS = [
    'rsi',        # RSI
    'macd',       # MACD
    'obv',        # OBV
    'adx',        # ADX
    'cmf'         # CMF
]

def detect_big_move_signals(df):
    """كشف الإشارات التي تسبق الارتفاعات الكبيرة"""
    signals = dict.fromkeys(INDICATORS, False)

    # التأكد من وجود بيانات كافية
    if len(df) < 20:
        return signals

    try:
        # RSI: منطقة البيع المفرط أو التحسن القوي
        if 'rsi' in df.columns and len(df) > 5:
            rsi_values = df['rsi'].iloc[-6:].values
            current_rsi = rsi_values[-1]
            
            # إشارات قوية للارتفاعات الكبيرة
            oversold_bounce = current_rsi > 35 and rsi_values[-2] <= 30  # ارتداد من البيع المفرط
            strong_momentum = all(rsi_values[i] < rsi_values[i+1] for i in range(4))  # زخم قوي
            healthy_range = 40 <= current_rsi <= 60  # منطقة صحية مع إمكانية نمو
            
            signals['rsi'] = oversold_bounce or (strong_momentum and healthy_range)

        # MACD: تقاطعات قوية أو زخم متزايد
        if 'macd' in df.columns and 'macd_signal' in df.columns and len(df) > 5:
            macd_values = df['macd'].iloc[-6:].values
            signal_values = df['macd_signal'].iloc[-6:].values
            
            current_macd = macd_values[-1]
            current_signal = signal_values[-1]
            prev_macd = macd_values[-2]
            prev_signal = signal_values[-2]
            
            # إشارات قوية
            golden_cross = (current_macd > current_signal and prev_macd <= prev_signal)  # تقاطع ذهبي
            accelerating = (current_macd > prev_macd and current_macd > 0)  # تسارع فوق الصفر
            divergence_closing = abs(current_macd - current_signal) < abs(prev_macd - prev_signal)  # تقارب
            
            signals['macd'] = golden_cross or (accelerating and divergence_closing)

        # OBV: تدفق حجم قوي ومستمر
        if 'obv' in df.columns and len(df) >= 10:
            obv_values = df['obv'].iloc[-10:].values
            
            # اتجاه صاعد قوي في الحجم
            strong_volume_trend = all(obv_values[i] < obv_values[i+1] for i in range(7))
            recent_acceleration = obv_values[-1] > obv_values[-3] * 1.02  # تسارع حديث
            
            signals['obv'] = strong_volume_trend or recent_acceleration

        # ADX: قوة اتجاه عالية مع هيمنة صاعدة
        if all(col in df.columns for col in ['adx', 'adx_pos', 'adx_neg']) and len(df) > 3:
            current_adx = df['adx'].iloc[-1]
            current_pos = df['adx_pos'].iloc[-1]
            current_neg = df['adx_neg'].iloc[-1]
            prev_adx = df['adx'].iloc[-2]
            
            # قوة اتجاه عالية للارتفاعات الكبيرة
            strong_trend = current_adx > 30  # قوة عالية
            bullish_dominance = current_pos > current_neg * 2  # هيمنة صاعدة قوية
            increasing_strength = current_adx > prev_adx  # قوة متزايدة
            
            signals['adx'] = strong_trend and bullish_dominance and increasing_strength

        # CMF: تدفق نقدي قوي ومستمر
        if 'cmf' in df.columns and len(df) >= 5:
            cmf_values = df['cmf'].iloc[-5:].values
            
            # تدفق نقدي إيجابي قوي
            strong_inflow = all(val > 0.15 for val in cmf_values[-3:])  # تدفق قوي مستمر
            accelerating_inflow = cmf_values[-1] > cmf_values[-3]  # تسارع التدفق
            
            signals['cmf'] = strong_inflow or (cmf_values[-1] > 0.2 and accelerating_inflow)

    except Exception as e:
        print(f"⚠️ خطأ في كشف إشارات الارتفاعات الكبيرة: {e}")

    return signals

def analyze_file_big_moves(file_path):
    """تحليل ملف واحد للارتفاعات الكبيرة"""
    try:
        df = pd.read_csv(file_path)
        
        # التأكد من وجود الأعمدة المطلوبة
        required_columns = ['close', 'rsi', 'macd', 'macd_signal', 'obv']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return {}

        results = {}
        
        for i in range(50, len(df) - 12):  # ترك مساحة للتحليل
            signals = detect_big_move_signals(df.iloc[:i+1])
            
            # البحث عن ارتفاعات كبيرة في الـ 12 فترة القادمة
            current_price = df['close'].iloc[i]
            future_prices = df['close'].iloc[i+1:i+13]
            
            if len(future_prices) == 0:
                continue
                
            max_future_price = future_prices.max()
            max_price_change = (max_future_price - current_price) / current_price
            
            # التركيز على الارتفاعات المجدية (8%+)
            for periods in range(1, min(13, len(future_prices) + 1)):
                future_price = df['close'].iloc[i + periods]
                period_change = (future_price - current_price) / current_price

                # عتبة واقعية للارتفاعات المجدية
                success_threshold = 0.08  # 8% في أي إطار زمني (أكثر واقعية)
                
                if period_change >= success_threshold:
                    for indicator, signal in signals.items():
                        if signal:
                            if indicator not in results:
                                results[indicator] = {
                                    'total_signals': 0,
                                    'successful_signals': 0,
                                    'returns': [],
                                    'time_to_targets': []
                                }
                            
                            results[indicator]['total_signals'] += 1
                            results[indicator]['successful_signals'] += 1
                            results[indicator]['returns'].append(period_change * 100)
                            results[indicator]['time_to_targets'].append(periods)
                    break
            else:
                # لم يحقق ارتفاع كبير
                for indicator, signal in signals.items():
                    if signal:
                        if indicator not in results:
                            results[indicator] = {
                                'total_signals': 0,
                                'successful_signals': 0,
                                'returns': [],
                                'time_to_targets': []
                            }
                        results[indicator]['total_signals'] += 1
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في تحليل {file_path}: {e}")
        return {}

def main():
    print("🎯 بدء تحليل الارتفاعات المجدية - التركيز على أرباح 8%+")
    print("🚀 الهدف: إنتاج توصيات تحقق أرباح مجدية وواقعية")
    print(f"📁 البحث عن ملفات CSV في مجلد: {DATA_DIR}")
    
    # التحقق من وجود مجلد البيانات
    if not os.path.exists(DATA_DIR):
        print(f"❌ مجلد البيانات غير موجود: {DATA_DIR}")
        return
    
    # عد ملفات CSV
    csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]
    print(f"📁 وجد {len(csv_files)} ملف CSV في المجلد")
    
    # إحصائيات شاملة
    all_stats = {indicator: {
        'total_signals': 0,
        'successful_signals': 0,
        'returns': [],
        'time_to_targets': []
    } for indicator in INDICATORS}
    
    files_processed = 0
    
    for filename in csv_files:
        file_path = os.path.join(DATA_DIR, filename)
        
        file_results = analyze_file_big_moves(file_path)
        
        # دمج النتائج
        for indicator, stats in file_results.items():
            if indicator in all_stats:
                all_stats[indicator]['total_signals'] += stats['total_signals']
                all_stats[indicator]['successful_signals'] += stats['successful_signals']
                all_stats[indicator]['returns'].extend(stats['returns'])
                all_stats[indicator]['time_to_targets'].extend(stats['time_to_targets'])
        
        files_processed += 1
        
        if files_processed % 10 == 0:
            print(f"📈 تم تحليل {files_processed}/{len(csv_files)} ملف...")
    
    # حساب الإحصائيات النهائية للارتفاعات الكبيرة
    final_weights = {}
    
    for indicator, stats in all_stats.items():
        if stats['total_signals'] > 5:  # حد أدنى للإشارات
            success_rate = stats['successful_signals'] / stats['total_signals']
            avg_return = np.mean(stats['returns']) if stats['returns'] else 0
            avg_time = np.mean(stats['time_to_targets']) if stats['time_to_targets'] else 0
            
            # قبول أي مؤشر له أداء إيجابي مع الارتفاعات المجدية
            if success_rate > 0.15 and avg_return >= 8.0:  # 15% نجاح و 8% عائد (واقعي ومجدي)
                # حساب نسبة شارب
                returns_std = np.std(stats['returns']) if len(stats['returns']) > 1 else 1
                sharpe_ratio = avg_return / returns_std if returns_std > 0 else 0
                
                # حساب الوزن: احتمالية × عائد × استقرار
                weight = success_rate * avg_return * max(0.1, min(2.0, sharpe_ratio / 100))
                
                final_weights[indicator] = {
                    'weight': weight,
                    'success_probability': success_rate,
                    'average_return': avg_return,
                    'average_time_to_target': avg_time,
                    'sharpe_ratio': sharpe_ratio,
                    'total_signals': stats['total_signals'],
                    'successful_signals': stats['successful_signals']
                }
    
    # تطبيع الأوزان
    total_weight = sum(v['weight'] for v in final_weights.values())
    if total_weight > 0:
        for indicator in final_weights:
            final_weights[indicator]['weight'] = final_weights[indicator]['weight'] / total_weight
    
    # حفظ النتائج
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(final_weights, f, indent=4, ensure_ascii=False)
    
    # عرض النتائج
    print(f"\n✅ تم تحليل {files_processed} ملف")
    print(f"🎯 إحصائيات الارتفاعات الكبيرة (15%+):")
    print("-" * 80)
    
    if final_weights:
        # ترتيب المؤشرات حسب الوزن
        sorted_indicators = sorted(final_weights.items(), key=lambda x: x[1]['weight'], reverse=True)
        
        for indicator, stats in sorted_indicators:
            print(f"\n🔹 {indicator}:")
            print(f"   الوزن: {stats['weight']:.4f}")
            print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
            print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
            print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
            print(f"   نسبة شارب: {stats['sharpe_ratio']:.3f}")
            print(f"   إجمالي الإشارات: {stats['total_signals']:,}")
            print(f"   الإشارات الناجحة: {stats['successful_signals']:,}")
        
        print(f"\n📊 ملخص:")
        print(f"   المؤشرات المقبولة: {len(final_weights)}/5")
        print(f"   إجمالي الأوزان: {sum(v['weight'] for v in final_weights.values()):.3f}")
        
        # حساب متوسط الأداء العام
        avg_success = np.mean([v['success_probability'] for v in final_weights.values()])
        avg_return = np.mean([v['average_return'] for v in final_weights.values()])
        total_signals = sum([v['total_signals'] for v in final_weights.values()])
        
        print(f"\n🚀 الأداء للارتفاعات الكبيرة:")
        print(f"   متوسط احتمالية النجاح: {avg_success:.1%}")
        print(f"   متوسط العائد المتوقع: {avg_return:+.2f}%")
        print(f"   إجمالي إشارات الارتفاعات الكبيرة: {total_signals:,}")
        
    else:
        print("\n⚠️ لا توجد مؤشرات تحقق معايير الارتفاعات الكبيرة!")
        print("   المعايير: احتمالية >10%, عائد ≥15%")
    
    print(f"\n📁 تم حفظ أوزان الارتفاعات الكبيرة في {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
