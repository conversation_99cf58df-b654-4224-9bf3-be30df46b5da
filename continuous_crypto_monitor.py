#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام المراقبة المستمرة للعملات المشفرة
يعمل بشكل مستمر مع فواصل زمنية ذكية
"""

import time
import json
import logging
from datetime import datetime, timedelta
from integrated_crypto_system import IntegratedCryptoSystem
from config import *

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('continuous_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ContinuousCryptoMonitor:
    """نظام المراقبة المستمرة"""
    
    def __init__(self):
        self.system = IntegratedCryptoSystem()
        self.running = False
        self.last_recommendations = {}  # تخزين آخر التوصيات
        self.recommendation_cooldown = {}  # فترة انتظار لكل عملة
        
        # إعدادات المراقبة
        self.scan_interval = 300  # 5 دقائق بين كل فحص
        self.crypto_delay = 2     # ثانيتان بين كل عملة
        self.cooldown_hours = 6   # 6 ساعات قبل إعادة إرسال نفس التوصية
        
    def should_send_recommendation(self, symbol, current_rec):
        """تحديد ما إذا كان يجب إرسال التوصية أم لا"""
        current_time = datetime.now()
        
        # التحقق من فترة الانتظار
        if symbol in self.recommendation_cooldown:
            cooldown_time = self.recommendation_cooldown[symbol]
            if current_time < cooldown_time:
                return False
        
        # التحقق من التغيير في التوصية
        if symbol in self.last_recommendations:
            last_rec = self.last_recommendations[symbol]
            
            # إذا تغيرت الاحتمالية بشكل كبير (أكثر من 10%)
            prob_change = abs(current_rec['probability'] - last_rec['probability'])
            if prob_change > 0.10:
                return True
            
            # إذا تغير السعر المستهدف بشكل كبير (أكثر من 5%)
            target_change = abs(current_rec['target_price'] - last_rec['target_price']) / last_rec['target_price']
            if target_change > 0.05:
                return True
            
            return False
        
        return True  # توصية جديدة
    
    def update_recommendation_tracking(self, symbol, recommendation):
        """تحديث تتبع التوصيات"""
        self.last_recommendations[symbol] = recommendation
        self.recommendation_cooldown[symbol] = datetime.now() + timedelta(hours=self.cooldown_hours)
    
    def analyze_single_crypto(self, symbol):
        """تحليل عملة واحدة"""
        try:
            result = self.system.predict_crypto(symbol)
            
            if result and result['signal']:
                # التحقق من ضرورة الإرسال
                if self.should_send_recommendation(symbol, result):
                    # إرسال التوصية
                    stats = self.system.load_performance_stats()
                    message = self.system.format_recommendation_message(result, stats)
                    
                    if self.system.send_telegram_message(message):
                        logger.info(f"✅ تم إرسال توصية {symbol} - احتمالية: {result['probability']:.1%}")
                        
                        # تحديث التتبع
                        self.update_recommendation_tracking(symbol, result)
                        
                        # حفظ في التاريخ
                        history = self.system.load_recommendations_history()
                        history.append(result)
                        self.system.save_recommendations_history(history)
                        
                        return True
                    else:
                        logger.error(f"❌ فشل إرسال توصية {symbol}")
                else:
                    logger.info(f"⏳ تم تجاهل {symbol} - في فترة انتظار أو لا توجد تغييرات كبيرة")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل {symbol}: {e}")
            return False
    
    def run_continuous_scan(self):
        """تشغيل الفحص المستمر"""
        logger.info("🚀 بدء المراقبة المستمرة...")
        logger.info(f"⏱️ فحص كل {self.scan_interval} ثانية")
        logger.info(f"🔄 تأخير {self.crypto_delay} ثانية بين العملات")
        logger.info(f"⏳ فترة انتظار {self.cooldown_hours} ساعات لكل عملة")
        
        # تحميل النموذج مرة واحدة
        if not self.system.load_model():
            logger.error("❌ فشل تحميل النموذج")
            return
        
        self.running = True
        scan_count = 0
        
        try:
            while self.running:
                scan_count += 1
                start_time = time.time()
                
                logger.info(f"🔍 بدء الفحص #{scan_count}")
                
                # فحص التوصيات المنتهية الصلاحية
                self.system.check_expired_recommendations()
                
                # جلب قائمة العملات المحفوظة
                cryptos = self.system.get_all_saved_cryptos()
                logger.info(f"💰 سيتم فحص جميع العملات المحفوظة: {len(cryptos)} عملة")
                
                recommendations_sent = 0
                
                # فحص كل عملة مع تأخير
                for i, crypto in enumerate(cryptos):
                    if not self.running:
                        break
                    
                    logger.info(f"🔍 فحص {crypto} ({i+1}/{len(cryptos)})")
                    
                    if self.analyze_single_crypto(crypto):
                        recommendations_sent += 1
                    
                    # تأخير بين العملات
                    if i < len(cryptos) - 1:  # ليس آخر عملة
                        time.sleep(self.crypto_delay)
                
                end_time = time.time()
                scan_duration = end_time - start_time
                
                logger.info(f"✅ انتهى الفحص #{scan_count}")
                logger.info(f"📊 توصيات مرسلة: {recommendations_sent}")
                logger.info(f"⏱️ مدة الفحص: {scan_duration:.1f} ثانية")
                
                # انتظار حتى الفحص التالي
                if self.running:
                    logger.info(f"⏳ انتظار {self.scan_interval} ثانية للفحص التالي...")
                    time.sleep(self.scan_interval)
        
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف المراقبة بواسطة المستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ في المراقبة المستمرة: {e}")
        finally:
            self.running = False
    
    def stop(self):
        """إيقاف المراقبة"""
        self.running = False
        logger.info("⏹️ تم طلب إيقاف المراقبة")

def print_monitor_banner():
    """عرض شعار المراقبة المستمرة"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                🔄 نظام المراقبة المستمرة للعملات المشفرة                ║
║                                                              ║
║  🧠 فحص ذكي مستمر مع فواصل زمنية                                ║
║  📊 تجاهل التوصيات المكررة                                      ║
║  ⚡ إرسال فوري للفرص الجديدة                                   ║
║  📈 تتبع التغييرات في الاحتمالية والأسعار                        ║
║  🔄 فحص كل 5 دقائق مع تأخير 2 ثانية بين العملات               ║
║                                                              ║
║  المطور: نظام الذكاء الاصطناعي المتقدم                            ║
║  الإصدار: المراقبة المستمرة 2025                                ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """الوظيفة الرئيسية"""
    print_monitor_banner()
    
    monitor = ContinuousCryptoMonitor()
    
    try:
        print("\n🚀 بدء نظام المراقبة المستمرة...")
        print("⏹️ اضغط Ctrl+C لإيقاف المراقبة\n")
        
        monitor.run_continuous_scan()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام")
    finally:
        monitor.stop()

if __name__ == "__main__":
    main()
