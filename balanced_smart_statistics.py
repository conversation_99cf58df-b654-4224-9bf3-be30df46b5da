#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إحصائيات ذكي متوازن - 3 مؤشرات أساسية + 2 مؤشرات تكميلية
الاستراتيجية الذكية المتوازنة:
- احتمالية نجاح فوق 50% (واقعي)
- نسبة صعود أعلى من 6% (قابل للتحقيق)

المؤشرات الأساسية (متوفرة في جميع الملفات):
1. RSI - كشف البيع/الشراء المفرط (⭐⭐⭐⭐)
2. MACD - إشارات الموجات المتوسطة (⭐⭐⭐⭐)  
3. OBV - تدفق السيولة الحقيقية (⭐⭐⭐⭐)

المؤشرات التكميلية (إذا متوفرة):
4. ADX - قوة الاتجاه (⭐⭐⭐⭐)
5. CMF - فلترة الصعود الكاذب (⭐⭐⭐)
"""

import os
import pandas as pd
import json
import numpy as np

# إعدادات
DATA_DIR = "data"
OUTPUT_FILE = "balanced_smart_weights.json"

# المؤشرات الذكية المتوازنة
INDICATORS = [
    'rsi_smart',        # RSI ذكي مع الدعوم والمقاومات
    'macd_smart',       # MACD مع تقاطعات قوية
    'obv_smart',        # OBV تدفق السيولة الحقيقية
    'adx_smart',        # ADX قوة الاتجاه (إذا متوفر)
    'cmf_smart'         # CMF فلترة الصعود الكاذب (إذا متوفر)
]

def detect_balanced_smart_signals(df):
    """كشف الإشارات الذكية المتوازنة"""
    signals = dict.fromkeys(INDICATORS, False)

    # التأكد من وجود بيانات كافية
    if len(df) < 20:
        return signals

    try:
        # === RSI الذكي: مع الدعوم والمقاومات ===
        if 'rsi' in df.columns:
            current_rsi = df['rsi'].iloc[-1]
            prev_rsi = df['rsi'].iloc[-2] if len(df) > 1 else current_rsi
            
            # RSI في منطقة صحية ومتحسن
            signals['rsi_smart'] = (
                25 <= current_rsi <= 70 and  # منطقة صحية موسعة
                current_rsi > prev_rsi and   # تحسن
                current_rsi > 30             # خروج من البيع المفرط
            )

        # === MACD الذكي: تقاطعات وتحسن ===
        if 'macd' in df.columns and 'macd_signal' in df.columns:
            current_macd = df['macd'].iloc[-1]
            current_signal = df['macd_signal'].iloc[-1]
            prev_macd = df['macd'].iloc[-2] if len(df) > 1 else current_macd
            prev_signal = df['macd_signal'].iloc[-2] if len(df) > 1 else current_signal
            
            # تقاطع إيجابي أو تحسن فوق خط الصفر
            bullish_cross = (current_macd > current_signal and prev_macd <= prev_signal)
            improving_trend = (current_macd > prev_macd and current_macd > current_signal)
            
            signals['macd_smart'] = bullish_cross or improving_trend

        # === OBV الذكي: تدفق السيولة المستمر ===
        if 'obv' in df.columns:
            if len(df) >= 5:
                current_obv = df['obv'].iloc[-1]
                obv_sma = df['obv'].rolling(window=5).mean().iloc[-1]
                prev_obv_sma = df['obv'].rolling(window=5).mean().iloc[-2] if len(df) > 5 else obv_sma
                
                # OBV متحسن ومتزايد
                signals['obv_smart'] = (
                    current_obv > obv_sma * 0.98 and  # قريب أو فوق المتوسط
                    obv_sma > prev_obv_sma * 0.99     # اتجاه متزايد
                )

        # === ADX الذكي: قوة الاتجاه (إذا متوفر) ===
        if all(col in df.columns for col in ['adx', 'adx_pos', 'adx_neg']):
            current_adx = df['adx'].iloc[-1]
            current_pos = df['adx_pos'].iloc[-1]
            current_neg = df['adx_neg'].iloc[-1]
            
            # اتجاه صاعد مع قوة معقولة
            signals['adx_smart'] = (
                current_adx > 20 and           # قوة اتجاه معقولة
                current_pos > current_neg      # اتجاه صاعد
            )

        # === CMF الذكي: تدفق نقدي إيجابي (إذا متوفر) ===
        if 'cmf' in df.columns:
            current_cmf = df['cmf'].iloc[-1]
            
            # تدفق نقدي إيجابي
            signals['cmf_smart'] = current_cmf > 0.05  # تدفق إيجابي معقول

    except Exception as e:
        print(f"⚠️ خطأ في كشف الإشارات الذكية: {e}")

    return signals

def analyze_file_balanced(file_path):
    """تحليل ملف واحد بالاستراتيجية المتوازنة"""
    try:
        df = pd.read_csv(file_path)
        
        # التأكد من وجود الأعمدة الأساسية
        required_columns = ['close', 'rsi', 'macd', 'macd_signal', 'obv']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"⚠️ أعمدة أساسية مفقودة في {file_path}: {missing_columns}")
            return {}

        results = {}
        
        for i in range(50, len(df) - 12):  # ترك مساحة للتحليل
            signals = detect_balanced_smart_signals(df.iloc[:i+1])
            
            # البحث عن صعود معقول في الـ 12 فترة القادمة
            current_price = df['close'].iloc[i]
            future_prices = df['close'].iloc[i+1:i+13]
            
            if len(future_prices) == 0:
                continue
                
            max_future_price = future_prices.max()
            max_price_change = (max_future_price - current_price) / current_price
            
            # معايير نجاح متوازنة وواقعية
            for periods in range(1, min(13, len(future_prices) + 1)):
                future_price = df['close'].iloc[i + periods]
                period_change = (future_price - current_price) / current_price
                
                # عتبات متوازنة للصعود المعقول
                if periods <= 3:  # خلال 12 ساعة
                    success_threshold = 0.04  # 4%
                elif periods <= 6:  # خلال 24 ساعة
                    success_threshold = 0.06  # 6%
                else:  # خلال 48+ ساعة
                    success_threshold = 0.08  # 8%
                
                if period_change >= success_threshold:
                    for indicator, signal in signals.items():
                        if signal:
                            if indicator not in results:
                                results[indicator] = {
                                    'total_signals': 0,
                                    'successful_signals': 0,
                                    'returns': [],
                                    'time_to_targets': []
                                }
                            
                            results[indicator]['total_signals'] += 1
                            results[indicator]['successful_signals'] += 1
                            results[indicator]['returns'].append(period_change * 100)
                            results[indicator]['time_to_targets'].append(periods)
                    break
            else:
                # لم يحقق النجاح
                for indicator, signal in signals.items():
                    if signal:
                        if indicator not in results:
                            results[indicator] = {
                                'total_signals': 0,
                                'successful_signals': 0,
                                'returns': [],
                                'time_to_targets': []
                            }
                        results[indicator]['total_signals'] += 1
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في تحليل {file_path}: {e}")
        return {}

def main():
    print("🧠 بدء التحليل الذكي المتوازن - 5 مؤشرات عالية الاعتماد...")
    print("🎯 عتبات متوازنة: احتمالية >50%, صعود >6%")
    print(f"📊 البحث عن ملفات CSV في مجلد: {DATA_DIR}")
    
    # التحقق من وجود مجلد البيانات
    if not os.path.exists(DATA_DIR):
        print(f"❌ مجلد البيانات غير موجود: {DATA_DIR}")
        return
    
    # عد ملفات CSV
    csv_files = [f for f in os.listdir(DATA_DIR) if f.endswith('.csv')]
    print(f"📁 وجد {len(csv_files)} ملف CSV في المجلد")
    
    # إحصائيات شاملة
    all_stats = {indicator: {
        'total_signals': 0,
        'successful_signals': 0,
        'returns': [],
        'time_to_targets': []
    } for indicator in INDICATORS}
    
    files_processed = 0
    
    for filename in csv_files:
        file_path = os.path.join(DATA_DIR, filename)
        print(f"🔍 تحليل: {filename}")
        
        file_results = analyze_file_balanced(file_path)
        
        # دمج النتائج
        for indicator, stats in file_results.items():
            if indicator in all_stats:
                all_stats[indicator]['total_signals'] += stats['total_signals']
                all_stats[indicator]['successful_signals'] += stats['successful_signals']
                all_stats[indicator]['returns'].extend(stats['returns'])
                all_stats[indicator]['time_to_targets'].extend(stats['time_to_targets'])
        
        files_processed += 1
        
        if files_processed % 50 == 0:
            print(f"📈 تم تحليل {files_processed}/{len(csv_files)} ملف...")
    
    # حساب الإحصائيات النهائية مع العتبات المتوازنة
    final_weights = {}
    
    for indicator, stats in all_stats.items():
        if stats['total_signals'] > 50:  # حد أدنى معقول للإشارات
            success_rate = stats['successful_signals'] / stats['total_signals']
            avg_return = np.mean(stats['returns']) if stats['returns'] else 0
            avg_time = np.mean(stats['time_to_targets']) if stats['time_to_targets'] else 0
            
            # تطبيق العتبات المتوازنة
            if success_rate >= 0.50 and avg_return >= 6.0:  # 50% نجاح و 6% عائد
                # حساب نسبة شارب (العائد / المخاطر)
                returns_std = np.std(stats['returns']) if len(stats['returns']) > 1 else 1
                sharpe_ratio = avg_return / returns_std if returns_std > 0 else 0
                
                # حساب الوزن: احتمالية × عائد × جودة
                weight = success_rate * avg_return * max(0.1, sharpe_ratio / 100)
                
                final_weights[indicator] = {
                    'weight': weight,
                    'success_probability': success_rate,
                    'average_return': avg_return,
                    'average_time_to_target': avg_time,
                    'sharpe_ratio': sharpe_ratio,
                    'total_signals': stats['total_signals'],
                    'successful_signals': stats['successful_signals']
                }
    
    # تطبيع الأوزان
    total_weight = sum(v['weight'] for v in final_weights.values())
    if total_weight > 0:
        for indicator in final_weights:
            final_weights[indicator]['weight'] = final_weights[indicator]['weight'] / total_weight
    
    # حفظ النتائج
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(final_weights, f, indent=4, ensure_ascii=False)
    
    # عرض النتائج
    print(f"\n✅ تم تحليل {files_processed} ملف")
    print(f"🧠 إحصائيات المؤشرات الذكية المتوازنة:")
    print("-" * 80)
    
    if final_weights:
        # ترتيب المؤشرات حسب الوزن
        sorted_indicators = sorted(final_weights.items(), key=lambda x: x[1]['weight'], reverse=True)
        
        for indicator, stats in sorted_indicators:
            print(f"\n🔹 {indicator}:")
            print(f"   الوزن: {stats['weight']:.4f}")
            print(f"   احتمالية النجاح: {stats['success_probability']:.1%}")
            print(f"   متوسط العائد: {stats['average_return']:+.2f}%")
            print(f"   متوسط الوقت للهدف: {stats['average_time_to_target']:.1f} فترات")
            print(f"   نسبة شارب: {stats['sharpe_ratio']:.3f}")
            print(f"   إجمالي الإشارات: {stats['total_signals']:,}")
            print(f"   الإشارات الناجحة: {stats['successful_signals']:,}")
        
        print(f"\n📊 ملخص:")
        print(f"   المؤشرات المقبولة: {len(final_weights)}/5")
        print(f"   إجمالي الأوزان: {sum(v['weight'] for v in final_weights.values()):.3f}")
    else:
        print("\n⚠️ لا توجد مؤشرات تحقق العتبات المتوازنة!")
        print("   العتبات: احتمالية ≥50%, عائد ≥6%")
    
    print(f"\n📁 تم حفظ الأوزان الذكية المتوازنة في {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
