import json
import pandas as pd
import numpy as np
from datetime import datetime
import os

class AdvancedAnalytics:
    """أداة التحليل المتقدم للإحصائيات"""
    
    def __init__(self, weights_file='advanced_weights.json'):
        self.weights_file = weights_file
        self.weights = self.load_weights()
    
    def load_weights(self):
        """تحميل الأوزان المتقدمة"""
        try:
            with open(self.weights_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ خطأ في تحميل الأوزان: {e}")
            return {}
    
    def display_indicator_rankings(self):
        """عرض ترتيب المؤشرات حسب الفعالية"""
        if not self.weights:
            print("❌ لا توجد بيانات للعرض")
            return
        
        print("🏆 ترتيب المؤشرات حسب الفعالية")
        print("=" * 60)
        
        # ترتيب المؤشرات حسب نسبة شارب
        sorted_indicators = sorted(
            self.weights.items(), 
            key=lambda x: x[1].get('sharpe_ratio', 0), 
            reverse=True
        )
        
        for i, (indicator, stats) in enumerate(sorted_indicators, 1):
            print(f"\n{i}. {indicator}")
            print(f"   🎯 احتمالية النجاح: {stats.get('success_probability', 0):.1%}")
            print(f"   💰 متوسط العائد: {stats.get('average_return', 0):+.2f}%")
            print(f"   ⏰ متوسط الوقت للهدف: {stats.get('average_time_to_target', 0):.1f} فترات")
            print(f"   📊 نسبة شارب: {stats.get('sharpe_ratio', 0):.2f}")
            print(f"   🔢 إجمالي الإشارات: {stats.get('total_signals', 0)}")
            print(f"   ✅ الإشارات الناجحة: {stats.get('successful_signals', 0)}")
    
    def calculate_portfolio_metrics(self, selected_indicators=None):
        """حساب مقاييس المحفظة للمؤشرات المختارة"""
        if not self.weights:
            return None
        
        if selected_indicators is None:
            selected_indicators = list(self.weights.keys())
        
        # حساب المتوسطات المرجحة
        total_weight = sum(self.weights[ind].get('weight', 0) for ind in selected_indicators if ind in self.weights)
        
        if total_weight == 0:
            return None
        
        weighted_probability = sum(
            self.weights[ind].get('success_probability', 0) * self.weights[ind].get('weight', 0)
            for ind in selected_indicators if ind in self.weights
        ) / total_weight
        
        weighted_return = sum(
            self.weights[ind].get('average_return', 0) * self.weights[ind].get('weight', 0)
            for ind in selected_indicators if ind in self.weights
        ) / total_weight
        
        weighted_time = sum(
            self.weights[ind].get('average_time_to_target', 0) * self.weights[ind].get('weight', 0)
            for ind in selected_indicators if ind in self.weights
        ) / total_weight
        
        # حساب التنويع (عدد المؤشرات النشطة)
        diversification_score = len(selected_indicators) / len(self.weights)
        
        return {
            'expected_probability': weighted_probability,
            'expected_return': weighted_return,
            'expected_time': weighted_time,
            'diversification_score': diversification_score,
            'total_weight': total_weight
        }
    
    def simulate_trading_scenarios(self):
        """محاكاة سيناريوهات التداول المختلفة"""
        print("\n🎮 محاكاة سيناريوهات التداول")
        print("=" * 50)
        
        scenarios = {
            'محافظ': ['adx_bullish', 'macd_bullish', 'ema10_above_ema50'],
            'متوازن': ['adx_bullish', 'macd_bullish', 'stoch_rsi_bullish', 'obv_bullish'],
            'عدواني': list(self.weights.keys())[:6],  # أفضل 6 مؤشرات
            'عالي المخاطر': list(self.weights.keys())  # جميع المؤشرات
        }
        
        for scenario_name, indicators in scenarios.items():
            metrics = self.calculate_portfolio_metrics(indicators)
            if metrics:
                print(f"\n📊 سيناريو {scenario_name}:")
                print(f"   🎯 احتمالية النجاح: {metrics['expected_probability']:.1%}")
                print(f"   💰 العائد المتوقع: {metrics['expected_return']:+.2f}%")
                print(f"   ⏰ الوقت المتوقع: {metrics['expected_time']:.1f} فترات ({metrics['expected_time']*4:.1f} ساعة)")
                print(f"   🔀 نقاط التنويع: {metrics['diversification_score']:.1%}")
                print(f"   📈 نسبة المخاطر/العائد: 1:{metrics['expected_return']/5:.1f}")
    
    def generate_optimization_report(self):
        """إنشاء تقرير تحسين الاستراتيجية"""
        print("\n🔧 تقرير تحسين الاستراتيجية")
        print("=" * 50)
        
        if not self.weights:
            print("❌ لا توجد بيانات للتحليل")
            return
        
        # تحليل أفضل المؤشرات
        best_probability = max(self.weights.values(), key=lambda x: x.get('success_probability', 0))
        best_return = max(self.weights.values(), key=lambda x: x.get('average_return', 0))
        best_sharpe = max(self.weights.values(), key=lambda x: x.get('sharpe_ratio', 0))
        
        print("🏅 أفضل المؤشرات:")
        for indicator, stats in self.weights.items():
            if stats == best_probability:
                print(f"   🎯 أعلى احتمالية نجاح: {indicator} ({stats['success_probability']:.1%})")
            if stats == best_return:
                print(f"   💰 أعلى عائد متوسط: {indicator} ({stats['average_return']:+.2f}%)")
            if stats == best_sharpe:
                print(f"   📊 أفضل نسبة شارب: {indicator} ({stats['sharpe_ratio']:.2f})")
        
        # توصيات التحسين
        print("\n💡 توصيات التحسين:")
        
        # المؤشرات عالية الأداء
        high_performance = [
            ind for ind, stats in self.weights.items()
            if stats.get('success_probability', 0) > 0.6 and stats.get('average_return', 0) > 3
        ]
        
        if high_performance:
            print(f"   ✅ ركز على المؤشرات عالية الأداء: {', '.join(high_performance)}")
        
        # المؤشرات ضعيفة الأداء
        low_performance = [
            ind for ind, stats in self.weights.items()
            if stats.get('success_probability', 0) < 0.4 or stats.get('average_return', 0) < 1
        ]
        
        if low_performance:
            print(f"   ⚠️ راجع المؤشرات ضعيفة الأداء: {', '.join(low_performance)}")
        
        # توصيات العتبات
        avg_probability = np.mean([stats.get('success_probability', 0) for stats in self.weights.values()])
        avg_return = np.mean([stats.get('average_return', 0) for stats in self.weights.values()])
        
        print(f"\n🎚️ توصيات العتبات:")
        print(f"   احتمالية النجاح الدنيا المقترحة: {avg_probability:.1%}")
        print(f"   العائد المتوقع الأدنى المقترح: {avg_return:.1f}%")
    
    def export_trading_signals_analysis(self, output_file='trading_signals_analysis.json'):
        """تصدير تحليل شامل لإشارات التداول"""
        if not self.weights:
            print("❌ لا توجد بيانات للتصدير")
            return
        
        analysis = {
            'generated_at': datetime.now().isoformat(),
            'total_indicators': len(self.weights),
            'summary_statistics': {},
            'indicator_rankings': {},
            'trading_scenarios': {},
            'optimization_recommendations': {}
        }
        
        # إحصائيات عامة
        probabilities = [stats.get('success_probability', 0) for stats in self.weights.values()]
        returns = [stats.get('average_return', 0) for stats in self.weights.values()]
        times = [stats.get('average_time_to_target', 0) for stats in self.weights.values()]
        
        analysis['summary_statistics'] = {
            'avg_success_probability': np.mean(probabilities),
            'avg_return': np.mean(returns),
            'avg_time_to_target': np.mean(times),
            'best_probability': max(probabilities),
            'best_return': max(returns),
            'fastest_time': min([t for t in times if t > 0])
        }
        
        # ترتيب المؤشرات
        sorted_by_sharpe = sorted(
            self.weights.items(),
            key=lambda x: x[1].get('sharpe_ratio', 0),
            reverse=True
        )
        
        analysis['indicator_rankings'] = {
            'by_sharpe_ratio': [(ind, stats['sharpe_ratio']) for ind, stats in sorted_by_sharpe],
            'by_probability': sorted(
                [(ind, stats.get('success_probability', 0)) for ind, stats in self.weights.items()],
                key=lambda x: x[1], reverse=True
            ),
            'by_return': sorted(
                [(ind, stats.get('average_return', 0)) for ind, stats in self.weights.items()],
                key=lambda x: x[1], reverse=True
            )
        }
        
        # سيناريوهات التداول
        scenarios = {
            'conservative': ['adx_bullish', 'macd_bullish', 'ema10_above_ema50'],
            'balanced': ['adx_bullish', 'macd_bullish', 'stoch_rsi_bullish', 'obv_bullish'],
            'aggressive': [ind for ind, _ in sorted_by_sharpe[:6]],
            'high_risk': list(self.weights.keys())
        }
        
        for scenario_name, indicators in scenarios.items():
            metrics = self.calculate_portfolio_metrics(indicators)
            if metrics:
                analysis['trading_scenarios'][scenario_name] = metrics
        
        # حفظ التحليل
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        print(f"📁 تم حفظ التحليل الشامل في {output_file}")

def main():
    """الوظيفة الرئيسية"""
    print("📊 أداة التحليل المتقدم لإشارات التداول")
    print("=" * 60)
    
    analytics = AdvancedAnalytics()
    
    if not analytics.weights:
        print("❌ تأكد من تشغيل 'statistics - New.py' أولاً لإنشاء الإحصائيات")
        return
    
    # عرض التحليلات
    analytics.display_indicator_rankings()
    analytics.simulate_trading_scenarios()
    analytics.generate_optimization_report()
    
    # تصدير التحليل الشامل
    analytics.export_trading_signals_analysis()
    
    print(f"\n✅ تم الانتهاء من التحليل المتقدم")

if __name__ == "__main__":
    main()
