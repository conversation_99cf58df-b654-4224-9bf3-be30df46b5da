import ccxt
import pandas as pd
import ta
import requests
import time
import os
from datetime import datetime

# === إعداد تليجرام ===
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN", "**********************************************")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID", "766367805")

def send_telegram(message):
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    data = {'chat_id': CHAT_ID, 'text': message, 'parse_mode': 'Markdown'}
    try:
        requests.post(url, data=data)
    except Exception as e:
        print(f"❌ فشل إرسال الرسالة: {e}")

# === تحميل قائمة العملات من ملف خارجي ===
def load_symbols(filename="symbols.txt"):
    if not os.path.exists(filename):
        print(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    with open(filename, "r") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

# === تحليل عملة واحدة ===
def analyze_symbol(symbol):
    exchange = ccxt.binance()
    try:
        ohlcv = exchange.fetch_ohlcv(symbol, '15m', limit=100)
    except Exception as e:
        print(f"❌ فشل تحميل بيانات {symbol}: {e}")
        return

    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

    # إضافة مؤشرات
    df['ema50'] = df['close'].ewm(span=50).mean()
    df['ema200'] = df['close'].ewm(span=200).mean()
    df['rsi'] = ta.momentum.RSIIndicator(df['close']).rsi()
    df['stoch_rsi'] = ta.momentum.StochRSIIndicator(df['close']).stochrsi_k()
    macd = ta.trend.MACD(df['close'])
    df['macd'] = macd.macd_diff()
    df['adx'] = ta.trend.ADXIndicator(df['high'], df['low'], df['close']).adx()
    bb = ta.volatility.BollingerBands(df['close'])
    df['bb_high'] = bb.bollinger_hband()
    df['bb_low'] = bb.bollinger_lband()
    df['obv'] = ta.volume.OnBalanceVolumeIndicator(df['close'], df['volume']).on_balance_volume()
    df['cmf'] = ((2 * df['close'] - df['low'] - df['high']) / (df['high'] - df['low']).replace(0, 0.0001)) * df['volume']

    avg_vol = df['volume'].iloc[-20:-1].mean()
    last_close = df['close'].iloc[-1]
    last_open = df['open'].iloc[-1]
    last_volume = df['volume'].iloc[-1]
    price_change_percent = ((last_close - last_open) / last_open) * 100

    score = 0
    total_weight = 0
    reasons = []
    missing = []

    def check(condition, description, weight):
        nonlocal score, reasons, total_weight
        total_weight += weight
        if condition:
            score += weight
            reasons.append(f"✅ {description}")
        else:
            missing.append(f"❌ {description}")

    # المؤشرات الفنية + الأوزان
    check(df['rsi'].iloc[-1] < 30, "RSI منخفض (تشبع بيعي)", 10)
    check(df['stoch_rsi'].iloc[-1] < 0.2, "StochRSI منخفض", 10)
    check(df['macd'].iloc[-1] > 0, "MACD إيجابي", 10)
    check(df['adx'].iloc[-1] > 25, "ADX قوي (>25)", 10)
    check(df['ema50'].iloc[-2] < df['ema200'].iloc[-2] and df['ema50'].iloc[-1] > df['ema200'].iloc[-1], "تقاطع EMA إيجابي", 10)
    check(last_close > df['bb_high'].iloc[-1], "اختراق بولينجر العلوي", 10)
    check(last_volume > avg_vol * 2 and price_change_percent > 1.5, "سيولة مرتفعة + صعود سعري", 10)
    check(df['obv'].iloc[-1] > df['obv'].iloc[-5], "OBV في اتجاه صاعد", 10)
    check(df['cmf'].iloc[-1] > 0.1, "CMF يشير لدخول أموال", 10)

    probability = (score / total_weight) * 100

    if probability >= 50:  # عتبة إرسال التنبيه
        msg = f"🚨 *تحليل {symbol}*\n"
        msg += "\n".join(reasons)
        msg += "\n\n🔎 إشارات غير متحققة:\n" + "\n".join(missing)
        msg += f"\n\n📊 *احتمالية الصعود:* {probability:.1f}%"
        msg += f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        print(msg)
        send_telegram(msg)

# === تنفيذ المراقبة الدائمة ===
def main():
    print("📡 بدأ المراقبة للعملات الصغيرة...")
    symbols = load_symbols()
    if not symbols:
        print("❌ لا توجد عملات لمراقبتها.")
        return
    while True:
        for symbol in symbols:
            analyze_symbol(symbol)
            time.sleep(1.5)
        print("✅ تم الفحص. سيتم إعادة الفحص خلال 15 دقيقة.\n")
        time.sleep(900)

if __name__ == "__main__":
    main()
