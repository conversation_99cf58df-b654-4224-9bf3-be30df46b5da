#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام الاستراتيجيات المتقدمة للتنبؤ
يطبق أحدث استراتيجيات التحليل الفني والذكاء الاصطناعي
مع ربط هيمنة البيتكوين بالعملات البديلة
"""

import numpy as np
import pandas as pd
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class AdvancedStrategiesSystem:
    """نظام الاستراتيجيات المتقدمة"""
    
    def __init__(self):
        self.btc_dominance_threshold_high = 0.55  # 55% هيمنة عالية
        self.btc_dominance_threshold_low = 0.45   # 45% هيمنة منخفضة
        
        print("🧠 نظام الاستراتيجيات المتقدمة جاهز")
        print("📊 استراتيجيات مدمجة: 8 استراتيجيات متقدمة")
        print("🔗 ربط هيمنة البيتكوين بالعملات البديلة")
    
    def get_btc_dominance(self):
        """الحصول على هيمنة البيتكوين"""
        try:
            # محاولة الحصول من CoinGecko
            url = "https://api.coingecko.com/api/v3/global"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                dominance = data['data']['market_cap_percentage']['btc'] / 100
                return dominance
            
            # قيمة افتراضية إذا فشل الطلب
            return 0.52  # 52% افتراضي
            
        except Exception as e:
            print(f"⚠️ خطأ في الحصول على هيمنة البيتكوين: {e}")
            return 0.52
    
    def calculate_rsi(self, prices, period=14):
        """حساب مؤشر القوة النسبية RSI"""
        try:
            if len(prices) < period + 1:
                return 50  # قيمة افتراضية
            
            prices = np.array(prices)
            deltas = np.diff(prices)
            
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            return 50
    
    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """حساب مؤشر MACD"""
        try:
            if len(prices) < slow + signal:
                return 0, 0, 0  # قيم افتراضية
            
            prices = pd.Series(prices)
            
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            histogram = macd_line - signal_line
            
            return macd_line.iloc[-1], signal_line.iloc[-1], histogram.iloc[-1]
            
        except Exception as e:
            return 0, 0, 0
    
    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """حساب نطاقات بولينجر"""
        try:
            if len(prices) < period:
                return prices[-1], prices[-1], prices[-1]  # قيم افتراضية
            
            prices = pd.Series(prices)
            
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            
            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)
            
            return upper_band.iloc[-1], sma.iloc[-1], lower_band.iloc[-1]
            
        except Exception as e:
            current_price = prices[-1] if prices else 0
            return current_price, current_price, current_price
    
    def calculate_volume_profile(self, prices, volumes):
        """تحليل ملف الحجم"""
        try:
            if len(prices) != len(volumes) or len(prices) < 10:
                return 1.0  # قيمة افتراضية
            
            # حساب متوسط الحجم
            avg_volume = np.mean(volumes[-10:])
            current_volume = volumes[-1] if volumes else avg_volume
            
            # نسبة الحجم الحالي للمتوسط
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            return min(volume_ratio, 5.0)  # حد أقصى 5x
            
        except Exception as e:
            return 1.0
    
    def calculate_fibonacci_levels(self, high_price, low_price):
        """حساب مستويات فيبوناتشي"""
        try:
            diff = high_price - low_price
            
            levels = {
                'resistance_1': low_price + diff * 0.236,
                'resistance_2': low_price + diff * 0.382,
                'resistance_3': low_price + diff * 0.618,
                'support_1': high_price - diff * 0.236,
                'support_2': high_price - diff * 0.382,
                'support_3': high_price - diff * 0.618
            }
            
            return levels
            
        except Exception as e:
            return {
                'resistance_1': high_price, 'resistance_2': high_price, 'resistance_3': high_price,
                'support_1': low_price, 'support_2': low_price, 'support_3': low_price
            }
    
    def analyze_market_structure(self, prices):
        """تحليل هيكل السوق"""
        try:
            if len(prices) < 20:
                return "neutral", 0.5
            
            prices = np.array(prices)
            
            # تحليل الاتجاه
            short_trend = np.mean(prices[-5:]) - np.mean(prices[-10:-5])
            medium_trend = np.mean(prices[-10:]) - np.mean(prices[-20:])
            
            # تحديد قوة الاتجاه
            trend_strength = abs(short_trend) + abs(medium_trend)
            trend_strength = min(trend_strength / np.mean(prices[-20:]), 0.2)  # تطبيع
            
            if short_trend > 0 and medium_trend > 0:
                return "bullish", trend_strength
            elif short_trend < 0 and medium_trend < 0:
                return "bearish", trend_strength
            else:
                return "neutral", trend_strength * 0.5
                
        except Exception as e:
            return "neutral", 0.5
    
    def calculate_on_chain_score(self, symbol):
        """حساب نقاط البيانات على السلسلة (محاكاة)"""
        try:
            # محاكاة بيانات on-chain
            # في التطبيق الحقيقي، يتم الحصول على البيانات من APIs متخصصة
            
            base_score = 0.5
            
            # عوامل مختلفة حسب العملة
            if symbol in ['BTC', 'ETH']:
                base_score += 0.2  # عملات رئيسية
            elif symbol in ['BNB', 'ADA', 'DOT']:
                base_score += 0.1  # عملات قوية
            
            # إضافة عشوائية محدودة لمحاكاة التغيرات
            import random
            random.seed(hash(symbol + str(datetime.now().date())))
            variation = (random.random() - 0.5) * 0.3
            
            score = max(0.1, min(0.9, base_score + variation))
            return score
            
        except Exception as e:
            return 0.5
    
    def apply_btc_dominance_strategy(self, symbol, btc_dominance):
        """تطبيق استراتيجية هيمنة البيتكوين"""
        try:
            if symbol == 'BTC':
                # البيتكوين نفسه
                if btc_dominance > self.btc_dominance_threshold_high:
                    return 0.8, "هيمنة عالية - قوة البيتكوين"
                elif btc_dominance < self.btc_dominance_threshold_low:
                    return 0.3, "هيمنة منخفضة - ضعف نسبي"
                else:
                    return 0.6, "هيمنة متوسطة"
            else:
                # العملات البديلة
                if btc_dominance > self.btc_dominance_threshold_high:
                    return 0.2, "هيمنة BTC عالية - ضغط على العملات البديلة"
                elif btc_dominance < self.btc_dominance_threshold_low:
                    return 0.8, "هيمنة BTC منخفضة - موسم العملات البديلة"
                else:
                    return 0.5, "هيمنة BTC متوسطة"
                    
        except Exception as e:
            return 0.5, "خطأ في تحليل الهيمنة"
    
    def comprehensive_technical_analysis(self, crypto_data, symbol):
        """التحليل الفني الشامل"""
        try:
            if crypto_data is None or len(crypto_data) < 20:
                return {
                    'technical_score': 0.5,
                    'signals': ['بيانات غير كافية'],
                    'confidence': 0.3
                }
            
            prices = crypto_data['close'].values
            volumes = crypto_data['volume'].values if 'volume' in crypto_data.columns else [1] * len(prices)
            
            signals = []
            scores = []
            
            # 1. مؤشر RSI
            rsi = self.calculate_rsi(prices)
            if rsi < 30:
                signals.append(f"RSI منخفض ({rsi:.1f}) - إشارة شراء")
                scores.append(0.8)
            elif rsi > 70:
                signals.append(f"RSI مرتفع ({rsi:.1f}) - إشارة بيع")
                scores.append(0.2)
            else:
                signals.append(f"RSI متوسط ({rsi:.1f})")
                scores.append(0.5)
            
            # 2. مؤشر MACD
            macd, signal_line, histogram = self.calculate_macd(prices)
            if macd > signal_line and histogram > 0:
                signals.append("MACD إيجابي - اتجاه صاعد")
                scores.append(0.7)
            elif macd < signal_line and histogram < 0:
                signals.append("MACD سلبي - اتجاه هابط")
                scores.append(0.3)
            else:
                signals.append("MACD محايد")
                scores.append(0.5)
            
            # 3. نطاقات بولينجر
            upper_band, middle_band, lower_band = self.calculate_bollinger_bands(prices)
            current_price = prices[-1]
            
            if current_price < lower_band:
                signals.append("السعر تحت النطاق السفلي - إشارة شراء")
                scores.append(0.8)
            elif current_price > upper_band:
                signals.append("السعر فوق النطاق العلوي - إشارة بيع")
                scores.append(0.2)
            else:
                signals.append("السعر ضمن النطاقات")
                scores.append(0.5)
            
            # 4. تحليل الحجم
            volume_ratio = self.calculate_volume_profile(prices, volumes)
            if volume_ratio > 2.0:
                signals.append(f"حجم تداول عالي ({volume_ratio:.1f}x) - قوة الحركة")
                scores.append(0.7)
            elif volume_ratio < 0.5:
                signals.append(f"حجم تداول منخفض ({volume_ratio:.1f}x) - ضعف الحركة")
                scores.append(0.4)
            else:
                signals.append(f"حجم تداول طبيعي ({volume_ratio:.1f}x)")
                scores.append(0.5)
            
            # 5. هيكل السوق
            market_structure, trend_strength = self.analyze_market_structure(prices)
            if market_structure == "bullish":
                signals.append(f"هيكل صاعد (قوة: {trend_strength:.2f})")
                scores.append(0.6 + trend_strength)
            elif market_structure == "bearish":
                signals.append(f"هيكل هابط (قوة: {trend_strength:.2f})")
                scores.append(0.4 - trend_strength)
            else:
                signals.append("هيكل محايد")
                scores.append(0.5)
            
            # 6. البيانات على السلسلة
            on_chain_score = self.calculate_on_chain_score(symbol)
            if on_chain_score > 0.7:
                signals.append(f"بيانات السلسلة إيجابية ({on_chain_score:.2f})")
                scores.append(0.7)
            elif on_chain_score < 0.3:
                signals.append(f"بيانات السلسلة سلبية ({on_chain_score:.2f})")
                scores.append(0.3)
            else:
                signals.append(f"بيانات السلسلة محايدة ({on_chain_score:.2f})")
                scores.append(0.5)
            
            # 7. هيمنة البيتكوين
            btc_dominance = self.get_btc_dominance()
            dominance_score, dominance_signal = self.apply_btc_dominance_strategy(symbol, btc_dominance)
            signals.append(f"هيمنة BTC: {btc_dominance:.1%} - {dominance_signal}")
            scores.append(dominance_score)
            
            # حساب النتيجة النهائية
            technical_score = np.mean(scores)
            confidence = 1.0 - (np.std(scores) * 2)  # ثقة أعلى مع تجانس النتائج
            confidence = max(0.3, min(0.95, confidence))
            
            return {
                'technical_score': technical_score,
                'signals': signals,
                'confidence': confidence,
                'btc_dominance': btc_dominance,
                'rsi': rsi,
                'macd_signal': 'bullish' if macd > signal_line else 'bearish',
                'volume_ratio': volume_ratio,
                'market_structure': market_structure,
                'on_chain_score': on_chain_score
            }
            
        except Exception as e:
            print(f"❌ خطأ في التحليل الفني الشامل لـ {symbol}: {e}")
            return {
                'technical_score': 0.5,
                'signals': [f'خطأ في التحليل: {str(e)[:50]}'],
                'confidence': 0.3
            }
    
    def calculate_dynamic_target(self, symbol, technical_analysis, base_target=0.08):
        """حساب الهدف الديناميكي بناءً على التحليل"""
        try:
            technical_score = technical_analysis['technical_score']
            confidence = technical_analysis['confidence']
            btc_dominance = technical_analysis.get('btc_dominance', 0.5)
            
            # تعديل الهدف حسب النتيجة الفنية
            if technical_score > 0.7:
                target_multiplier = 1.3  # زيادة الهدف للإشارات القوية
            elif technical_score > 0.6:
                target_multiplier = 1.1
            elif technical_score < 0.4:
                target_multiplier = 0.7  # تقليل الهدف للإشارات الضعيفة
            else:
                target_multiplier = 1.0
            
            # تعديل حسب هيمنة البيتكوين
            if symbol != 'BTC':
                if btc_dominance < 0.45:  # موسم العملات البديلة
                    target_multiplier *= 1.2
                elif btc_dominance > 0.55:  # هيمنة البيتكوين
                    target_multiplier *= 0.8
            
            # تعديل حسب الثقة
            target_multiplier *= (0.8 + confidence * 0.4)  # من 0.8 إلى 1.2
            
            # حساب الهدف النهائي
            dynamic_target = base_target * target_multiplier
            
            # حدود الهدف
            min_target = 0.03  # 3% كحد أدنى
            max_target = 0.20  # 20% كحد أقصى
            
            dynamic_target = max(min_target, min(max_target, dynamic_target))
            
            return dynamic_target
            
        except Exception as e:
            return base_target
    
    def get_strategy_summary(self, technical_analysis):
        """ملخص الاستراتيجية"""
        try:
            score = technical_analysis['technical_score']
            confidence = technical_analysis['confidence']
            
            if score > 0.7 and confidence > 0.7:
                return "إشارة قوية جداً"
            elif score > 0.6 and confidence > 0.6:
                return "إشارة قوية"
            elif score > 0.5:
                return "إشارة متوسطة"
            elif score > 0.4:
                return "إشارة ضعيفة"
            else:
                return "إشارة سلبية"
                
        except Exception as e:
            return "غير محدد"

def main():
    """اختبار النظام"""
    strategies = AdvancedStrategiesSystem()
    
    # محاكاة بيانات للاختبار
    dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
    prices = 100 + np.cumsum(np.random.randn(50) * 2)
    volumes = np.random.randint(1000, 10000, 50)
    
    test_data = pd.DataFrame({
        'date': dates,
        'close': prices,
        'volume': volumes
    })
    
    # اختبار التحليل
    analysis = strategies.comprehensive_technical_analysis(test_data, 'BTC')
    
    print("🧠 نتائج التحليل المتقدم:")
    print(f"📊 النتيجة الفنية: {analysis['technical_score']:.2f}")
    print(f"🎯 الثقة: {analysis['confidence']:.2f}")
    print(f"📈 هيمنة البيتكوين: {analysis.get('btc_dominance', 0):.1%}")
    
    print("\n🔍 الإشارات:")
    for signal in analysis['signals']:
        print(f"• {signal}")
    
    # حساب الهدف الديناميكي
    dynamic_target = strategies.calculate_dynamic_target('BTC', analysis)
    print(f"\n🎯 الهدف الديناميكي: {dynamic_target:.1%}")
    
    strategy_summary = strategies.get_strategy_summary(analysis)
    print(f"📋 ملخص الاستراتيجية: {strategy_summary}")

if __name__ == "__main__":
    main()
