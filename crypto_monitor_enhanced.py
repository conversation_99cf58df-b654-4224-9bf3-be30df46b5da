import ccxt
import pandas as pd
import ta
import requests
import time
import os
import json
import numpy as np
from datetime import datetime, timedelta
import logging

# === إعداد السجلات ===
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crypto_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# === إعداد تليجرام ===
BOT_TOKEN = os.getenv("TELEGRAM_TOKEN", "**********************************************")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID", "766367805")

# === إعدادات النظام ===
TIMEFRAME = '4h'  # تغيير إلى 4 ساعات لمطابقة البيانات المدربة
LIMIT = 100
WEIGHTS_FILE = 'weights.json'
MIN_SCORE_THRESHOLD = 0.6  # عتبة أعلى للتنبيهات
COOLDOWN_HOURS = 12  # فترة انتظار بين التنبيهات لنفس العملة

# === متغيرات عامة ===
last_alerts = {}  # تتبع آخر تنبيه لكل عملة
weights = {}

def load_weights():
    """تحميل أوزان المؤشرات من الملف"""
    global weights
    try:
        with open(WEIGHTS_FILE, 'r') as f:
            weights = json.load(f)
        logger.info(f"✅ تم تحميل الأوزان من {WEIGHTS_FILE}")
        return True
    except FileNotFoundError:
        logger.error(f"❌ لم يتم العثور على ملف {WEIGHTS_FILE}")
        # أوزان افتراضية
        weights = {
            "rsi_bullish": 0.12,
            "macd_bullish": 0.134,
            "adx_bullish": 0.195,
            "bollinger_breakout": 0.02,
            "ema20_above_ema50": 0.112,
            "volume_spike": 0.054,
            "obv_bullish": 0.126,
            "cmf_bullish": 0.111,
            "stoch_rsi_bullish": 0.128
        }
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في تحميل الأوزان: {e}")
        return False

def send_telegram(message):
    """إرسال رسالة تليجرام"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    data = {'chat_id': CHAT_ID, 'text': message, 'parse_mode': 'Markdown'}
    try:
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("✅ تم إرسال التنبيه بنجاح")
        else:
            logger.error(f"❌ فشل إرسال التنبيه: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ خطأ في إرسال التنبيه: {e}")

def load_symbols(filename="symbols.txt"):
    """تحميل قائمة العملات"""
    if not os.path.exists(filename):
        logger.error(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                # تحويل من صيغة ARK/USDT إلى ARKUSDT
                symbol = line.replace("/", "")
                symbols.append(symbol)
        return symbols

def can_send_alert(symbol):
    """فحص إمكانية إرسال تنبيه للعملة"""
    if symbol not in last_alerts:
        return True
    
    last_alert_time = last_alerts[symbol]
    time_diff = datetime.now() - last_alert_time
    return time_diff.total_seconds() > (COOLDOWN_HOURS * 3600)

def calculate_advanced_indicators(df):
    """حساب المؤشرات المتقدمة"""
    try:
        # RSI
        df['rsi'] = ta.momentum.RSIIndicator(close=df['close']).rsi()
        
        # MACD
        macd = ta.trend.MACD(close=df['close'])
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        
        # EMA
        df['ema20'] = ta.trend.EMAIndicator(close=df['close'], window=20).ema_indicator()
        df['ema50'] = ta.trend.EMAIndicator(close=df['close'], window=50).ema_indicator()
        
        # ADX
        adx = ta.trend.ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
        df['adx'] = adx.adx()
        
        # Bollinger Bands
        bb = ta.volatility.BollingerBands(close=df['close'])
        df['bb_upper'] = bb.bollinger_hband()
        df['bb_lower'] = bb.bollinger_lband()
        
        # OBV
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
        
        # CMF
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'], low=df['low'], close=df['close'], volume=df['volume']
        ).chaikin_money_flow()
        
        # Stochastic RSI
        stoch_rsi = ta.momentum.StochRSIIndicator(close=df['close'], window=14, smooth1=3, smooth2=3)
        df['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
        df['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
        
        # Volume Spike
        df['volume_avg_20'] = df['volume'].rolling(window=20).mean()
        df['volume_spike_ratio'] = df['volume'] / df['volume_avg_20']
        
        return df
    except Exception as e:
        logger.error(f"❌ خطأ في حساب المؤشرات: {e}")
        return None

def detect_bullish_signals(df):
    """كشف الإشارات الصاعدة باستخدام الأوزان المحسوبة"""
    if df is None or len(df) < 50:
        return None, []
    
    signals = {}
    active_signals = []
    
    try:
        # RSI Bullish (30-70 منطقة جيدة للشراء)
        rsi_val = df['rsi'].iloc[-1]
        signals['rsi_bullish'] = 30 <= rsi_val <= 70
        if signals['rsi_bullish']:
            active_signals.append(f"RSI: {rsi_val:.1f}")
        
        # MACD Bullish
        macd_val = df['macd'].iloc[-1]
        macd_signal_val = df['macd_signal'].iloc[-1]
        signals['macd_bullish'] = macd_val > macd_signal_val
        if signals['macd_bullish']:
            active_signals.append("MACD إيجابي")
        
        # ADX Strong Trend
        adx_val = df['adx'].iloc[-1]
        signals['adx_bullish'] = adx_val > 25
        if signals['adx_bullish']:
            active_signals.append(f"ADX: {adx_val:.1f}")
        
        # Bollinger Breakout
        close_val = df['close'].iloc[-1]
        bb_upper_val = df['bb_upper'].iloc[-1]
        signals['bollinger_breakout'] = close_val > bb_upper_val
        if signals['bollinger_breakout']:
            active_signals.append("اختراق بولينجر")
        
        # EMA Crossover
        ema20_val = df['ema20'].iloc[-1]
        ema50_val = df['ema50'].iloc[-1]
        signals['ema20_above_ema50'] = ema20_val > ema50_val
        if signals['ema20_above_ema50']:
            active_signals.append("EMA20 > EMA50")
        
        # Volume Spike
        volume_ratio = df['volume_spike_ratio'].iloc[-1]
        signals['volume_spike'] = volume_ratio > 1.5
        if signals['volume_spike']:
            active_signals.append(f"حجم مرتفع: {volume_ratio:.1f}x")
        
        # OBV Trend
        obv_current = df['obv'].iloc[-1]
        obv_prev = df['obv'].iloc[-5] if len(df) >= 5 else df['obv'].iloc[0]
        signals['obv_bullish'] = obv_current > obv_prev
        if signals['obv_bullish']:
            active_signals.append("OBV صاعد")
        
        # CMF Positive
        cmf_val = df['cmf'].iloc[-1]
        signals['cmf_bullish'] = cmf_val > 0
        if signals['cmf_bullish']:
            active_signals.append(f"CMF: {cmf_val:.3f}")
        
        # Stochastic RSI
        stoch_k = df['stoch_rsi_k'].iloc[-1]
        stoch_d = df['stoch_rsi_d'].iloc[-1]
        signals['stoch_rsi_bullish'] = stoch_k > stoch_d and stoch_k < 0.8
        if signals['stoch_rsi_bullish']:
            active_signals.append("StochRSI إيجابي")
        
        return signals, active_signals
        
    except Exception as e:
        logger.error(f"❌ خطأ في كشف الإشارات: {e}")
        return None, []

def calculate_score(signals):
    """حساب النقاط باستخدام الأوزان المحسوبة"""
    if not signals:
        return 0

    total_score = 0
    for signal_name, is_active in signals.items():
        if is_active and signal_name in weights:
            total_score += weights[signal_name]

    return total_score

def analyze_symbol(symbol):
    """تحليل عملة واحدة"""
    exchange = ccxt.binance()

    try:
        # تحميل البيانات
        ohlcv = exchange.fetch_ohlcv(symbol, TIMEFRAME, limit=LIMIT)
        if not ohlcv or len(ohlcv) < 50:
            logger.warning(f"⚠️ بيانات غير كافية لـ {symbol}")
            return

        # تحويل إلى DataFrame
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)

        # حساب المؤشرات
        df = calculate_advanced_indicators(df)
        if df is None:
            return

        # كشف الإشارات
        signals, active_signals = detect_bullish_signals(df)
        if signals is None:
            return

        # حساب النقاط
        score = calculate_score(signals)

        # فحص العتبة وإمكانية الإرسال
        if score >= MIN_SCORE_THRESHOLD and can_send_alert(symbol):

            # حساب معلومات إضافية
            current_price = df['close'].iloc[-1]
            price_change_24h = ((current_price - df['close'].iloc[-6]) / df['close'].iloc[-6]) * 100
            volume_24h = df['volume'].iloc[-6:].sum()

            # إنشاء الرسالة
            message = f"🚨 *إشارة صاعدة قوية*\n\n"
            message += f"💰 *العملة:* {symbol}\n"
            message += f"💵 *السعر الحالي:* ${current_price:.6f}\n"
            message += f"📈 *التغيير 24س:* {price_change_24h:+.2f}%\n"
            message += f"📊 *النقاط:* {score:.3f} ({score*100:.1f}%)\n\n"

            message += "✅ *الإشارات النشطة:*\n"
            for signal in active_signals:
                message += f"• {signal}\n"

            # إضافة تحليل المخاطر
            risk_level = "منخفض" if score > 0.8 else "متوسط" if score > 0.7 else "عالي"
            message += f"\n⚠️ *مستوى المخاطر:* {risk_level}\n"

            message += f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # إرسال التنبيه
            logger.info(f"📤 إرسال تنبيه لـ {symbol} - النقاط: {score:.3f}")
            send_telegram(message)

            # تسجيل وقت التنبيه
            last_alerts[symbol] = datetime.now()

        elif score >= 0.4:  # تسجيل الإشارات الضعيفة للمراجعة
            logger.info(f"📊 {symbol}: نقاط {score:.3f} - تحت العتبة")

    except Exception as e:
        logger.error(f"❌ خطأ في تحليل {symbol}: {e}")

def cleanup_old_alerts():
    """تنظيف التنبيهات القديمة"""
    current_time = datetime.now()
    to_remove = []

    for symbol, alert_time in last_alerts.items():
        if (current_time - alert_time).total_seconds() > (COOLDOWN_HOURS * 3600):
            to_remove.append(symbol)

    for symbol in to_remove:
        del last_alerts[symbol]

    if to_remove:
        logger.info(f"🧹 تم تنظيف {len(to_remove)} تنبيه قديم")

def main():
    """الوظيفة الرئيسية"""
    logger.info("🚀 بدء تشغيل نظام مراقبة العملات المحسن...")

    # تحميل الأوزان
    if not load_weights():
        logger.warning("⚠️ سيتم استخدام الأوزان الافتراضية")

    # تحميل العملات
    symbols = load_symbols()
    if not symbols:
        logger.error("❌ لا توجد عملات لمراقبتها")
        return

    logger.info(f"📋 تم تحميل {len(symbols)} عملة للمراقبة")
    logger.info(f"⚙️ الإعدادات: إطار زمني {TIMEFRAME}, عتبة النقاط {MIN_SCORE_THRESHOLD}")

    cycle_count = 0

    try:
        while True:
            cycle_count += 1
            start_time = time.time()

            logger.info(f"🔄 بدء الدورة #{cycle_count}")

            analyzed_count = 0
            alerts_sent = 0

            for i, symbol in enumerate(symbols):
                try:
                    analyze_symbol(symbol)
                    analyzed_count += 1

                    # فترة انتظار بين العملات لتجنب حدود API
                    time.sleep(2)

                    # عرض التقدم كل 20 عملة
                    if (i + 1) % 20 == 0:
                        logger.info(f"📊 تم تحليل {i + 1}/{len(symbols)} عملة")

                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة {symbol}: {e}")
                    continue

            # تنظيف التنبيهات القديمة
            cleanup_old_alerts()

            cycle_time = time.time() - start_time
            logger.info(f"✅ انتهت الدورة #{cycle_count} - تم تحليل {analyzed_count} عملة في {cycle_time:.1f} ثانية")

            # انتظار قبل الدورة التالية (15 دقيقة)
            wait_time = 900  # 15 دقيقة
            logger.info(f"⏳ انتظار {wait_time//60} دقيقة قبل الدورة التالية...")
            time.sleep(wait_time)

    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ عام في النظام: {e}")

if __name__ == "__main__":
    main()
