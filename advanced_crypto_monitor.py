#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام مراقبة العملات المتقدم متعدد الأطر الزمنية مع التعلم العميق
الميزات:
- تحليل متعدد الأطر الزمنية (1h, 4h, 1d)
- نموذج التعلم العميق LSTM + Attention
- فلاتر ذكية متقدمة
- تنبيهات عالية الدقة
"""

import ccxt
import pandas as pd
import numpy as np
import logging
import time
import json
import os
from datetime import datetime, timedelta
import requests
from deep_learning_model import MultiTimeframeCryptoPredictor

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_crypto_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# === إعدادات النظام المتقدم ===
TIMEFRAMES = ['1h', '4h', '1d']
LIMITS = {'1h': 100, '4h': 100, '1d': 100}
DATA_DIR = 'multi_timeframe_data'

# عتبات التنبيه المتقدمة
MIN_DEEP_LEARNING_PROBABILITY = 0.65  # 65% احتمالية من النموذج العميق
MIN_EXPECTED_RETURN = 4.0  # 4% عائد متوقع كحد أدنى
MIN_CONFIDENCE_SCORE = 75  # 75/100 نقاط ثقة

# فلاتر السوق المتقدمة
MIN_VOLUME_USD = 1000000  # مليون دولار حجم يومي
MIN_VOLATILITY = 0.025    # 2.5% تقلب يومي
MAX_VOLATILITY = 0.15     # 15% تقلب يومي (تجنب التلاعب)

# إعدادات التنبيهات
COOLDOWN_HOURS = 8
TELEGRAM_BOT_TOKEN = "YOUR_BOT_TOKEN"
TELEGRAM_CHAT_ID = "YOUR_CHAT_ID"

# === متغيرات عامة ===
last_alerts = {}
deep_learning_model = None

def load_symbols(filename="symbols.txt"):
    """تحميل قائمة العملات"""
    if not os.path.exists(filename):
        logger.error(f"⚠️ لم يتم العثور على ملف {filename}")
        return []
    
    with open(filename, "r") as f:
        symbols = []
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                symbols.append(line)
        return symbols

def initialize_deep_learning_model():
    """تهيئة نموذج التعلم العميق"""
    global deep_learning_model
    
    try:
        deep_learning_model = MultiTimeframeCryptoPredictor()
        
        # محاولة تحميل نموذج مدرب
        if deep_learning_model.load_model('crypto_deep_model_v1'):
            logger.info("✅ تم تحميل نموذج التعلم العميق المدرب")
        else:
            # إنشاء نموذج جديد
            deep_learning_model.build_model()
            logger.info("🧠 تم إنشاء نموذج التعلم العميق جديد")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة نموذج التعلم العميق: {e}")
        return False

def fetch_multi_timeframe_data(symbol):
    """جلب البيانات لجميع الأطر الزمنية"""
    exchange = ccxt.binance()
    data = {}
    
    try:
        for timeframe in TIMEFRAMES:
            limit = LIMITS[timeframe]
            
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            if not ohlcv or len(ohlcv) < 50:
                logger.warning(f"⚠️ بيانات غير كافية لـ {symbol} في {timeframe}")
                return None
            
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            data[timeframe] = df
            time.sleep(0.2)  # تجنب حدود API
        
        return data
        
    except Exception as e:
        logger.error(f"❌ خطأ في جلب بيانات {symbol}: {e}")
        return None

def apply_advanced_filters(symbol, data):
    """تطبيق الفلاتر المتقدمة"""
    try:
        # فلتر الحجم (استخدام بيانات 4h للدقة)
        df_4h = data['4h']
        avg_volume = df_4h['volume'].tail(24).mean()  # آخر 24 فترة (4 أيام)
        current_price = df_4h['close'].iloc[-1]
        volume_usd = avg_volume * current_price
        
        if volume_usd < MIN_VOLUME_USD:
            logger.info(f"📊 {symbol}: حجم منخفض ({volume_usd:,.0f} USD) - تم تجاهله")
            return False
        
        # فلتر التقلبات (استخدام بيانات 1h للحساسية)
        df_1h = data['1h']
        price_changes = df_1h['close'].pct_change().tail(24)
        volatility = price_changes.std()
        
        if volatility < MIN_VOLATILITY:
            logger.info(f"📊 {symbol}: تقلبات منخفضة ({volatility:.3f}) - تم تجاهله")
            return False
            
        if volatility > MAX_VOLATILITY:
            logger.info(f"📊 {symbol}: تقلبات عالية جداً ({volatility:.3f}) - مشبوه")
            return False
        
        # فلتر الاتجاه العام (استخدام بيانات 1d للاستقرار)
        df_1d = data['1d']
        if len(df_1d) >= 20:
            sma_10 = df_1d['close'].tail(10).mean()
            sma_20 = df_1d['close'].tail(20).mean()
            
            # تفضيل العملات في اتجاه صاعد عام
            trend_factor = 1.2 if current_price > sma_10 > sma_20 else 1.0
        else:
            trend_factor = 1.0
        
        # فلتر السيولة النسبية
        recent_volume = df_4h['volume'].tail(6).mean()  # آخر 24 ساعة
        historical_volume = df_4h['volume'].tail(72).mean()  # آخر 12 يوم
        
        volume_ratio = recent_volume / historical_volume if historical_volume > 0 else 1
        
        # تفضيل العملات مع نشاط حجم متزايد
        volume_factor = min(1.3, max(0.8, volume_ratio))
        
        return {
            'trend_factor': trend_factor,
            'volume_factor': volume_factor,
            'volatility': volatility,
            'volume_usd': volume_usd
        }
        
    except Exception as e:
        logger.error(f"❌ خطأ في فلترة {symbol}: {e}")
        return False

def get_deep_learning_prediction(symbol, data):
    """الحصول على تنبؤ من نموذج التعلم العميق"""
    global deep_learning_model
    
    if not deep_learning_model:
        return None
    
    try:
        # تحضير البيانات للنموذج
        prediction = deep_learning_model.predict(
            data['1h'], data['4h'], data['1d']
        )
        
        if prediction:
            logger.info(f"🧠 {symbol}: تنبؤ التعلم العميق - "
                       f"احتمالية: {prediction['probability']:.1%}, "
                       f"عائد: {prediction['expected_return']:+.2f}%, "
                       f"وقت: {prediction['time_to_target']:.1f}h")
        
        return prediction
        
    except Exception as e:
        logger.error(f"❌ خطأ في تنبؤ التعلم العميق لـ {symbol}: {e}")
        return None

def calculate_advanced_confidence_score(prediction, filters):
    """حساب نقاط الثقة المتقدمة"""
    if not prediction or not filters:
        return 0
    
    # نقاط الثقة الأساسية من النموذج
    base_score = prediction['probability'] * 100
    
    # مكافآت الفلاتر
    trend_bonus = (filters['trend_factor'] - 1) * 20  # حتى +4 نقاط
    volume_bonus = (filters['volume_factor'] - 1) * 15  # حتى +4.5 نقاط
    
    # مكافأة التقلبات المثلى
    optimal_volatility = 0.05  # 5% مثالي
    volatility_distance = abs(filters['volatility'] - optimal_volatility)
    volatility_bonus = max(0, 10 - volatility_distance * 200)  # حتى +10 نقاط
    
    # مكافأة الحجم العالي
    volume_millions = filters['volume_usd'] / 1000000
    volume_bonus = min(10, np.log10(volume_millions) * 5)  # حتى +10 نقاط
    
    # النتيجة النهائية
    total_score = base_score + trend_bonus + volume_bonus + volatility_bonus + volume_bonus
    
    return min(100, max(0, total_score))

def send_telegram_alert(message):
    """إرسال تنبيه تلغرام"""
    if not TELEGRAM_BOT_TOKEN or TELEGRAM_BOT_TOKEN == "YOUR_BOT_TOKEN":
        logger.info("📱 تنبيه تلغرام معطل (لم يتم تكوين البوت)")
        return
    
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
        data = {
            'chat_id': TELEGRAM_CHAT_ID,
            'text': message,
            'parse_mode': 'Markdown'
        }
        
        response = requests.post(url, data=data, timeout=10)
        if response.status_code == 200:
            logger.info("✅ تم إرسال التنبيه")
        else:
            logger.error(f"❌ فشل إرسال التنبيه: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ خطأ في إرسال التنبيه: {e}")

def analyze_symbol_advanced(symbol):
    """تحليل متقدم لعملة واحدة"""
    try:
        # جلب البيانات متعددة الأطر الزمنية
        data = fetch_multi_timeframe_data(symbol)
        if not data:
            return
        
        # تطبيق الفلاتر المتقدمة
        filters = apply_advanced_filters(symbol, data)
        if not filters:
            return
        
        # الحصول على تنبؤ التعلم العميق
        prediction = get_deep_learning_prediction(symbol, data)
        if not prediction:
            logger.info(f"📊 {symbol}: لا يمكن الحصول على تنبؤ - تم تجاهله")
            return
        
        # حساب نقاط الثقة المتقدمة
        confidence_score = calculate_advanced_confidence_score(prediction, filters)
        
        # تطبيق المكافآت
        adjusted_probability = prediction['probability'] * filters['trend_factor'] * filters['volume_factor']
        adjusted_return = prediction['expected_return'] * filters['trend_factor']
        
        # فحص العتبات
        if (adjusted_probability >= MIN_DEEP_LEARNING_PROBABILITY and 
            adjusted_return >= MIN_EXPECTED_RETURN and 
            confidence_score >= MIN_CONFIDENCE_SCORE):
            
            # فحص cooldown
            now = datetime.now()
            if symbol in last_alerts:
                time_diff = now - last_alerts[symbol]
                if time_diff.total_seconds() < COOLDOWN_HOURS * 3600:
                    logger.info(f"⏰ {symbol}: في فترة انتظار - تم تجاهله")
                    return
            
            last_alerts[symbol] = now
            
            # إنشاء رسالة التنبيه المتقدمة
            current_price = data['4h']['close'].iloc[-1]
            predicted_price = current_price * (1 + adjusted_return / 100)
            
            message = f"🚨 *إشارة متقدمة عالية الدقة*\n\n"
            message += f"💰 *العملة:* {symbol}\n"
            message += f"💵 *السعر الحالي:* ${current_price:.6f}\n"
            message += f"🔮 *السعر المتوقع:* ${predicted_price:.6f}\n\n"
            
            message += f"🧠 *التعلم العميق:*\n"
            message += f"   احتمالية: {adjusted_probability:.1%}\n"
            message += f"   عائد متوقع: {adjusted_return:+.2f}%\n"
            message += f"   وقت للهدف: {prediction['time_to_target']:.1f}h\n\n"
            
            message += f"📊 *تحليل متقدم:*\n"
            message += f"   نقاط الثقة: {confidence_score:.0f}/100\n"
            message += f"   مكافأة الاتجاه: {filters['trend_factor']:.2f}x\n"
            message += f"   مكافأة الحجم: {filters['volume_factor']:.2f}x\n"
            message += f"   التقلبات: {filters['volatility']:.1%}\n"
            message += f"   الحجم: ${filters['volume_usd']:,.0f}\n\n"
            
            message += f"🎯 *الأطر الزمنية:* 1h + 4h + 1d\n"
            message += f"🤖 *النموذج:* LSTM + Attention\n"
            message += f"🕒 {now.strftime('%Y-%m-%d %H:%M:%S')}"
            
            logger.info(f"📤 إرسال تنبيه متقدم لـ {symbol}")
            send_telegram_alert(message)
            
        else:
            logger.info(f"📊 {symbol}: احتمالية {adjusted_probability:.1%}, "
                       f"عائد {adjusted_return:+.2f}%, ثقة {confidence_score:.0f} - تحت العتبة")
    
    except Exception as e:
        logger.error(f"❌ خطأ في تحليل {symbol}: {e}")

def main():
    """الوظيفة الرئيسية المتقدمة"""
    logger.info("🚀 بدء تشغيل نظام مراقبة العملات المتقدم")
    logger.info("🧠 الميزات: تعلم عميق + أطر زمنية متعددة + فلاتر ذكية")
    
    # تهيئة نموذج التعلم العميق
    if not initialize_deep_learning_model():
        logger.error("❌ فشل في تهيئة نموذج التعلم العميق")
        return
    
    logger.info(f"🔧 العتبات المتقدمة: احتمالية {MIN_DEEP_LEARNING_PROBABILITY:.1%}, "
               f"عائد {MIN_EXPECTED_RETURN}%, ثقة {MIN_CONFIDENCE_SCORE}")
    
    # تحميل قائمة العملات
    symbols = load_symbols()
    if not symbols:
        logger.error("❌ لا توجد عملات لمراقبتها")
        return
    
    logger.info(f"📋 تم تحميل {len(symbols)} عملة للمراقبة المتقدمة")
    
    try:
        while True:
            logger.info(f"🔄 بدء دورة تحليل متقدمة - فحص {len(symbols)} عملة")
            
            for i, symbol in enumerate(symbols, 1):
                logger.info(f"🔍 [{i}/{len(symbols)}] تحليل متقدم {symbol}")
                analyze_symbol_advanced(symbol)
                time.sleep(3)  # وقت أطول للتحليل المتقدم
            
            logger.info("✅ انتهت الدورة المتقدمة - انتظار 20 دقيقة")
            time.sleep(1200)  # 20 دقيقة للتحليل المتقدم
            
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في النظام: {e}")

if __name__ == "__main__":
    main()
