#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام التحديث التلقائي للبيانات
يحدث البيانات كل ساعة ويقوم بدورات تحليل مستمرة
"""

import time
import threading
from datetime import datetime, timedelta
from integrated_crypto_system import IntegratedCryptoSystem
from live_data_system import LiveDataManager

class AutoUpdateSystem:
    """نظام التحديث التلقائي"""
    
    def __init__(self):
        self.crypto_system = IntegratedCryptoSystem()
        self.live_data = LiveDataManager()
        self.running = False
        
        # إعدادات التحديث
        self.data_update_interval = 3600  # ساعة واحدة
        self.analysis_interval = 1800     # 30 دقيقة
        self.rest_interval = 300          # 5 دقائق راحة
        
        print("🔄 نظام التحديث التلقائي جاهز")
        print("📊 تحديث البيانات: كل ساعة")
        print("🔍 دورة التحليل: كل 30 دقيقة")
        print("⏸️ فترة الراحة: 5 دقائق")
    
    def update_all_data(self):
        """تحديث جميع البيانات"""
        try:
            print(f"🔄 بدء تحديث البيانات - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # جلب قائمة العملات
            symbols = self.live_data.get_all_available_symbols()
            print(f"📊 سيتم تحديث {len(symbols)} عملة")
            
            # تحديث البيانات
            updated_count = self.live_data.update_all_cryptos(symbols, max_workers=15)
            
            print(f"✅ تم تحديث {updated_count} عملة بنجاح")
            return updated_count
            
        except Exception as e:
            print(f"❌ خطأ في تحديث البيانات: {e}")
            return 0
    
    def run_analysis_cycle(self):
        """تشغيل دورة تحليل واحدة"""
        try:
            print(f"🔍 بدء دورة التحليل - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # تحميل النموذج إذا لم يكن محملاً
            if not self.crypto_system.model_loaded:
                if not self.crypto_system.load_model():
                    print("❌ فشل تحميل النموذج")
                    return False
            
            # فحص التوصيات المنتهية الصلاحية
            self.crypto_system.check_expired_recommendations()
            
            # تشغيل التحليل
            start_time = time.time()
            recommendations_count = self.crypto_system.analyze_and_send()
            end_time = time.time()
            
            analysis_time = end_time - start_time
            print(f"✅ انتهت دورة التحليل في {analysis_time:.1f} ثانية")
            print(f"📊 توصيات جديدة: {recommendations_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في دورة التحليل: {e}")
            return False
    
    def data_update_loop(self):
        """حلقة تحديث البيانات"""
        while self.running:
            try:
                self.update_all_data()
                
                # انتظار حتى التحديث التالي
                print(f"⏰ انتظار {self.data_update_interval/3600:.1f} ساعة للتحديث التالي...")
                time.sleep(self.data_update_interval)
                
            except Exception as e:
                print(f"❌ خطأ في حلقة تحديث البيانات: {e}")
                time.sleep(300)  # انتظار 5 دقائق عند الخطأ
    
    def analysis_loop(self):
        """حلقة التحليل المستمر"""
        while self.running:
            try:
                # تشغيل دورة التحليل
                self.run_analysis_cycle()
                
                # فترة راحة
                print(f"⏸️ فترة راحة {self.rest_interval/60:.1f} دقيقة...")
                time.sleep(self.rest_interval)
                
                # انتظار حتى الدورة التالية
                remaining_time = self.analysis_interval - self.rest_interval
                print(f"⏰ انتظار {remaining_time/60:.1f} دقيقة للدورة التالية...")
                time.sleep(remaining_time)
                
            except Exception as e:
                print(f"❌ خطأ في حلقة التحليل: {e}")
                time.sleep(300)  # انتظار 5 دقائق عند الخطأ
    
    def start(self):
        """بدء النظام التلقائي"""
        if self.running:
            print("⚠️ النظام يعمل بالفعل")
            return
        
        self.running = True
        
        print("🚀 بدء النظام التلقائي...")
        
        # تحديث أولي للبيانات
        print("📊 تحديث أولي للبيانات...")
        self.update_all_data()
        
        # بدء خيوط التشغيل
        data_thread = threading.Thread(target=self.data_update_loop, daemon=True)
        analysis_thread = threading.Thread(target=self.analysis_loop, daemon=True)
        
        data_thread.start()
        analysis_thread.start()
        
        print("✅ تم بدء النظام التلقائي بنجاح")
        print("🔄 خيط تحديث البيانات: نشط")
        print("🔍 خيط التحليل المستمر: نشط")
        
        return data_thread, analysis_thread
    
    def stop(self):
        """إيقاف النظام التلقائي"""
        self.running = False
        print("⏹️ تم إيقاف النظام التلقائي")
    
    def get_status(self):
        """الحصول على حالة النظام"""
        return {
            'running': self.running,
            'data_update_interval': self.data_update_interval,
            'analysis_interval': self.analysis_interval,
            'rest_interval': self.rest_interval
        }

def print_banner():
    """عرض شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                🔄 نظام التحديث التلقائي للعملات المشفرة                ║
║                                                              ║
║  📊 تحديث البيانات كل ساعة من مصادر متعددة                        ║
║  🔍 تحليل مستمر كل 30 دقيقة                                   ║
║  💰 أسعار حية ودقيقة من Binance + CoinGecko                  ║
║  📈 إرسال تلقائي للتوصيات عبر التليجرام                          ║
║  ⏸️ فترات راحة ذكية لتجنب الحمل الزائد                          ║
║                                                              ║
║  المطور: نظام الذكاء الاصطناعي المتقدم                            ║
║  الإصدار: النظام التلقائي 2025                                  ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    auto_system = AutoUpdateSystem()
    
    try:
        print("\n🚀 بدء النظام التلقائي...")
        print("⏹️ اضغط Ctrl+C لإيقاف النظام\n")
        
        # بدء النظام
        threads = auto_system.start()
        
        # انتظار إشارة الإيقاف
        while auto_system.running:
            time.sleep(1)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم طلب إيقاف النظام...")
        auto_system.stop()
        print("✅ تم إيقاف النظام بأمان")
    
    except Exception as e:
        print(f"\n❌ خطأ في النظام: {e}")
        auto_system.stop()

if __name__ == "__main__":
    main()
